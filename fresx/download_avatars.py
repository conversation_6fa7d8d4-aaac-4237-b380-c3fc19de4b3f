#!/usr/bin/env python3
"""
Fresx头像下载脚本
使用Unsplash API下载与主题相关的头像图片
"""

import requests
import os
import json
from urllib.parse import urlparse

# Unsplash API配置
ACCESS_KEY = "sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM"
BASE_URL = "https://api.unsplash.com"

# 搜索关键词（与Fresx主题相关）
SEARCH_KEYWORDS = [
    "portrait professional",
    "avatar minimalist", 
    "face artistic",
    "person creative",
    "profile modern",
    "headshot clean"
]

def download_image(url, filename):
    """下载图片到指定路径"""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ 下载成功: {filename}")
        return True
    except Exception as e:
        print(f"❌ 下载失败 {filename}: {e}")
        return False

def search_photos(query, per_page=2):
    """搜索Unsplash照片"""
    url = f"{BASE_URL}/search/photos"
    params = {
        'query': query,
        'per_page': per_page,
        'orientation': 'squarish',  # 方形图片适合头像
        'content_filter': 'high',
        'order_by': 'relevant'
    }
    headers = {
        'Authorization': f'Client-ID {ACCESS_KEY}'
    }
    
    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        print(f"❌ 搜索失败 '{query}': {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始下载Fresx头像图片...")
    
    # 创建assets目录
    assets_dir = "assets/images/avatars"
    os.makedirs(assets_dir, exist_ok=True)
    
    downloaded_count = 0
    target_count = 6
    
    for keyword in SEARCH_KEYWORDS:
        if downloaded_count >= target_count:
            break
            
        print(f"\n🔍 搜索关键词: {keyword}")
        results = search_photos(keyword, per_page=2)
        
        if not results or 'results' not in results:
            continue
            
        for i, photo in enumerate(results['results']):
            if downloaded_count >= target_count:
                break
                
            # 获取中等尺寸的图片URL
            image_url = photo['urls']['regular']
            
            # 生成文件名
            filename = f"fre_avatar_{downloaded_count + 1:02d}.jpg"
            filepath = os.path.join(assets_dir, filename)
            
            # 下载图片
            if download_image(image_url, filepath):
                downloaded_count += 1
                
                # 保存图片信息
                info = {
                    'id': photo['id'],
                    'description': photo.get('description', ''),
                    'alt_description': photo.get('alt_description', ''),
                    'photographer': photo['user']['name'],
                    'photographer_url': photo['user']['links']['html'],
                    'download_url': photo['links']['download_location']
                }
                
                info_file = os.path.join(assets_dir, f"{filename}.json")
                with open(info_file, 'w', encoding='utf-8') as f:
                    json.dump(info, f, indent=2, ensure_ascii=False)
    
    print(f"\n✨ 下载完成！共下载 {downloaded_count} 张头像图片")
    print(f"📁 保存位置: {assets_dir}")
    
    # 更新pubspec.yaml中的assets配置提示
    print("\n📝 请在pubspec.yaml中添加以下配置:")
    print("  assets:")
    print("    - assets/images/avatars/")

if __name__ == "__main__":
    main()
