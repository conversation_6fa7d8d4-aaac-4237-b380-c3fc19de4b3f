# Fresx - 冰箱异味去除全技巧对话社交应用

## 项目概述

Fresx是一款专注于冰箱异味去除的AI对话社交应用。通过与10位专业的AI除味师对话，用户可以获得个性化的冰箱除味解决方案，同时通过打卡功能获得金币奖励，形成良好的冰箱维护习惯。

**应用名称**: Fresx
**品类**: 社交品类
**关键词**: Fridge, Odor, AI, Freshness, Kitchen

## 目标用户

面向有冰箱异味困扰的家庭用户，特别是：
- 经常遇到冰箱异味问题的家庭主妇/主夫
- 注重厨房卫生和食品保鲜的用户
- 喜欢通过AI对话获取专业建议的科技爱好者
- 希望养成良好生活习惯的用户

## 商店描述

**Fresx — Your AI Fridge Odor Fixers**

A fridge reeking of old leftovers, yet banishing smells feels hit-or-miss. Picking natural absorbers, targeting stubborn scents, avoiding temporary fixes—every effort risks lingering stench.

<PERSON><PERSON><PERSON>, your team of AI kitchen care experts, steps in. Stuck between baking soda or citrus, or unsure of placement? Fresx's AIs help. The Odor Analyzer identifies sources, the Absorber Picker suggests solutions, and the Placement Guide shares tips. Chat to tweak methods, fix misses, and keep fridges fresh.

## 核心功能

### 1. AI对话功能
- **10位专业AI除味师**: 每位AI都有独特的专业领域和语言风格
- **智能对话**: 基于Moonshot文本生成模型，提供专业的除味建议
- **打字机效果**: AI回复采用逐字显示，增强沉浸感
- **免费次数限制**: 每个角色默认3次免费聊天机会

### 2. 打卡奖励系统
- **每日打卡**: 在"我的"页面显示打卡模块
- **金币奖励机制**:
  - 第1天: 20金币
  - 第2-3天: 50金币/天
  - 第4-7天: 80金币/天
  - 7天为一个周期

### 3. 聊天记录管理
- **本地存储**: 使用SQLite数据库存储聊天记录
- **历史查看**: 可查看以往的对话记录
- **记录管理**: 支持手动删除单条或清空所有历史记录

## AI角色设定

### 1. 蔬果除味师 - VeggieFresh
**短介绍**: Teach removing fridge odors from fruits and veggies.
**长介绍**: Store produce in breathable bags, wipe spoiled items promptly. Place a bowl of citrus peels in the crisper to neutralize smells, replace weekly.

### 2. 肉类除味师 - MeatOdorFix
**短介绍**: Guide eliminating fridge odors from meat and seafood.
**长介绍**: Seal raw meat in airtight containers, clean spills immediately with vinegar. Place a small dish of activated charcoal near the meat drawer to absorb odors.

### 3. 乳制品除味师 - DairyFresh
**短介绍**: Instruct removing fridge smells from milk, cheese, etc.
**长介绍**: Tightly seal dairy containers, check expiration dates often. Wipe shelves with baking soda solution if spills occur, leave a lemon wedge on the door shelf.

### 4. 熟食除味师 - CookedFoodFresh
**短介绍**: Provide tips to eliminate odors from leftover meals.
**长介绍**: Store leftovers in lidded glass containers, label with dates. Freeze excess to prevent spoilage, wipe containers with lemon juice before reuse.

### 5. 冰箱角落除味师 - CornerFresh
**短介绍**: Teach cleaning hidden fridge spots to remove odors.
**长介绍**: Pull out drawers monthly, scrub with soapy water + baking soda. Use a small brush to clean gasket crevices, dry thoroughly to prevent mold smells.

### 6. 天然除味剂师 - NaturalDeodor
**短介绍**: Guide making homemade fridge odor absorbers.
**长介绍**: Fill a jar with baking soda + a few drops of lemon oil, punch holes in the lid. Or use a bowl of oats—renew every 2 weeks for continuous freshness.

### 7. 深度清洁除味师 - DeepCleanFresh
**短介绍**: Instruct thorough fridge cleaning to banish odors.
**长介绍**: Empty fridge, turn off, wipe all surfaces with vinegar. Leave doors open 2 hours, place a bowl of coffee grounds inside before restocking.

### 8. 异味预防师 - OdorStop
**短介绍**: Offer tips to prevent fridge odors from forming.
**长介绍**: Check food weekly, discard expired items. Use separate containers for strong-smelling foods (onions, garlic), keep a deodorizer in place year-round.

### 9. 小型冰箱除味师 - MiniFridgeFresh
**短介绍**: Teach removing odors from compact fridges.
**长介绍**: Use a small sachet of baking soda, replace monthly. Wipe interior with diluted bleach (rinsed well) for tough smells, avoid overpacking to let air circulate.

### 10. 持久清新师 - LongFresh
**短介绍**: Provide ways to keep fridges smelling fresh long-term.
**长介绍**: Rotate deodorizers (baking soda, charcoal, citrus) monthly. Line drawers with baking soda-soaked paper towels, replace when they harden.

## 配色方案

### 主品牌色（Primary）
- **清新薄荷绿**: #2ECC71
  - 心理联想：绿色代表清新、自然、健康，与冰箱保鲜、去除异味的主题高度契合
  - 使用场景：主按钮、顶部导航栏、重要图标与选中状态

### 辅助色（Secondary / Accent）
- **亮橙色**: #F39C12
  - 心理联想：橙色活泼、有活力，与"打卡奖励"的温暖感和金币联动相得益彰
  - 使用场景：打卡奖励提示、徽章背景、提示徽标等次要但需要吸引注意的位置
- **柠檬黄**: #F7DC6F
  - 心理联想：黄色清亮、明快，与柠檬、香气、中和气味的自然印象契合
  - 使用场景：卡片背景、Hover效果、信息提醒背景

### 中性色（Neutrals）
- **深炭灰**: #333333 - 主文本颜色，确保信息阅读清晰、舒适
- **柔和灰**: #888888 - 次级文本、辅助说明、图标线条、分隔线等辅助界面元素
- **背景浅灰**: #F5F5F5 - 整体应用背景色，提供清爽明净的界面体验

### 强调/状态色（Feedback）
- **亮红色**: #E74C3C - 代表错误、失败、警告，如清洁失败、放置不当等提示
- **鲜蓝色**: #3498DB - 代表信息状态，如系统提示、帮助说明、AI聊天入口等

### AI角色配色建议
- **VeggieFresh 蔬果除味师**: 柠檬黄 + 绿
- **MeatOdorFix 肉类除味师**: 沉稳灰色 + 橙黄色
- **DairyFresh 乳制品除味师**: 柔和蓝 + 黄
- **CookedFoodFresh 熟食除味师**: 暖橙色 + 浅灰
- **CornerFresh/DeepCleanFresh/OdorStop/LongFresh**: 浅灰 + 主品牌绿
- **MiniFridgeFresh 小型冰箱除味师**: 柔和灰 + 柠檬黄

### 打卡模块配色
- **模块背景**: 浅灰背景 + 柠檬黄卡片
- **金币数字**: 亮橙色或金黄色文字
- **完成勾选/状态标记**: 清新薄荷绿勾勾
- **未完成状态**: 柔和灰或淡红色提示

## 技术架构

### 状态管理和设计模式
- **状态管理**: MobX
- **设计模式**: MVC架构
- **数据库**: SQLite（本地存储）
- **AI引擎**: Moonshot文本生成模型

### 核心架构目录（MobX + MVC分层）

#### 1. 数据实体层 → FreshCore
- **含义**: Fresh（保鲜）+ Core（核心），替代"Models"
- **内容**:
  - 冰箱档案 ChillProfile（温度/湿度/异味类型）
  - 除味配方库 RemedyFormula（活性炭吸附法、柠檬中和法等方案）
- **技术实现**: 实体类通过MobX的@observable实现响应式状态绑定

#### 2. 界面交互层 → FrostAtelier
- **含义**: Frost（霜）+ Atelier（工坊），替代"Screens"
- **内容**:
  - 除味工作台 DeodorBench（实时异味检测与治理界面）
  - 方案展厅 RemedyGallery（用户共享的除味方案库）

#### 3. 业务协调层 → PurifyMaster
- **含义**: Purify（净化）+ Master（大师），替代"Controllers"
- **MobX整合**:
  - Store定义 → ScentStore（管理异味强度/治理进度状态）
  - Action逻辑 → BlastAction（执行活性炭吸附、通风等操作）

#### 4. 状态管理层 → AromaFlow
- **含义**: Aroma（气味）+ Flow（流动），替代"State"
- **结构**:
  - 异味检测流 OdorStream（@observable实时监测异味等级）
  - 治理进度库 CureVault（存储用户操作记录与方案效果）

### 扩展功能目录

#### 5. AI治理引擎 → ScentSage
- **含义**: Scent（气味）+ Sage（智者），封装AI除味方案推荐算法
- **内容**:
  - 源头分析器 SourceSniffer（识别异味来源：食物腐败/细菌滋生）
  - 配方生成器 BlendWhisper（动态生成小苏打+柠檬的混合方案）

#### 6. 社交协作层 → ColdSquare
- **含义**: Cold（冷藏）+ Square（广场），支持用户交流除味经验
- **内容**:
  - 实时治理室 LivePurifyRoom（多人协作诊断冰箱异味）
  - 灵感布告板 TipBoard（用户分享茶叶包/橘子皮等偏方）

#### 7. 知识库 → CureArchive
- **含义**: Cure（治理）+ Archive（档案库），存储科学方法与用户案例
- **内容**:
  - 科学手册 TechFolio（活性炭吸附原理、通风标准指南）
  - 偏方案例库 HackCase（面团吸附法、檀香皂使用技巧）

### 支持性目录

#### 8. UI组件库 → IceChips
- **含义**: Ice（冰）+ Chips（碎片），提供制冷风格组件
- **组件例**:
  - 异味雷达 OdorRadar（可视化异味分布的热力图）
  - 材料选择器 StuffPicker（活性炭/茶叶/小苏打等材质预览）

#### 9. 配置与路由 → FrostRoutes
- **含义**: Frost（霜冻）+ Routes（路径），定义导航流与主题
- **内容**:
  - 治理流 PurifyTrail（检测→选择方案→执行→分享）
  - 主题色库 ChillPalette（活性炭黑#333333、冷冻蓝#1E90FF）

## 页面结构规划

| 页面名称 | 文件名 | 主要功能 | 导航方式 |
|---------|--------|----------|----------|
| 首页（AI角色选择） | FresAiRoleSelectVie.dart | 展示10个AI角色，支持标签分类和角色选择 | Tab导航-首页 |
| AI聊天页面 | FresChatDialogVie.dart | 与选定AI角色进行对话，显示剩余次数 | Push导航 |
| 聊天历史页面 | FresLtianHistoryVie.dart | 查看历史对话记录，支持删除管理 | Tab导航-历史 |
| 我的页面 | FresVipCenterVie.dart | 用户信息、打卡模块、收藏角色管理 | Tab导航-我的 |
| 用户信息编辑 | FresVipEditVie.dart | 自定义头像与昵称 | Push导航 |
| 收藏角色管理 | FresFavoriteRoleVie.dart | 管理喜欢的AI角色 | Push导航 |
| 反馈页面 | FresFebkVie.dart | 用户反馈和建议提交 | Push导航 |

## 数据模型

### 核心数据实体

#### 1. AI角色模型 (AiRoleModel)
```dart
class AiRoleModel {
  String id;              // 角色唯一标识
  String name;            // 角色名称（如VeggieFresh）
  String shortDesc;       // 短介绍
  String longDesc;        // 长介绍
  String avatarUrl;       // 头像图片URL
  List<String> tags;      // 标签分类
  Color primaryColor;     // 角色主色调
  Color secondaryColor;   // 角色辅助色
  int freeChats;         // 剩余免费聊天次数
  bool isFavorite;       // 是否收藏
}
```

#### 2. 聊天消息模型 (ChatMessageModel)
```dart
class ChatMessageModel {
  String id;              // 消息唯一标识
  String roleId;          // AI角色ID
  String content;         // 消息内容
  bool isUser;           // 是否为用户消息
  DateTime timestamp;     // 时间戳
  MessageStatus status;   // 消息状态（发送中/已发送/失败）
}
```

#### 3. 用户信息模型 (UserProfileModel)
```dart
class UserProfileModel {
  String userId;          // 用户ID
  String nickname;        // 昵称
  String avatarPath;      // 头像本地路径
  int totalCoins;        // 总金币数
  DateTime lastCheckIn;   // 最后打卡时间
  int checkInStreak;     // 连续打卡天数
  List<String> favoriteRoles; // 收藏的角色ID列表
}
```

#### 4. 打卡记录模型 (CheckInModel)
```dart
class CheckInModel {
  String id;              // 记录ID
  DateTime date;          // 打卡日期
  int coinsEarned;       // 获得金币数
  int streakDay;         // 连续天数
}
```

## 技术实现细节

### 状态管理架构 (MobX)
- **ScentStore**: 管理AI角色状态、聊天状态、用户状态
- **ChatStore**: 管理聊天消息、历史记录、打字机效果
- **UserStore**: 管理用户信息、打卡状态、金币系统

### 数据持久化 (SQLite)
- **ai_roles表**: 存储AI角色信息和用户交互数据
- **chat_messages表**: 存储所有聊天消息记录
- **user_profile表**: 存储用户个人信息
- **check_in_records表**: 存储打卡记录

### AI集成 (Moonshot API)
- **文本生成**: 基于角色特点生成个性化回复
- **情感分析**: 识别用户语气，调整AI回复风格
- **上下文管理**: 维护对话上下文，提供连贯的交互体验

### UI/UX特性
- **打字机效果**: 使用Timer实现逐字显示动画
- **响应式设计**: 适配不同iPhone屏幕尺寸
- **无障碍支持**: 支持VoiceOver和Dynamic Type
- **流畅动画**: 使用Flutter Animation框架

## 开发状态跟踪

| 页面/组件名称 | 开发状态 | 文件路径 | 备注 |
|--------------|----------|----------|------|
| 首页（AI角色选择） | [ ] 未开始 | lib/FrostAtelier/FresAiRoleSelectVie.dart | 10个AI角色展示 |
| AI聊天页面 | [ ] 未开始 | lib/FrostAtelier/FresChatDialogVie.dart | 对话界面+打字机效果 |
| 聊天历史页面 | [ ] 未开始 | lib/FrostAtelier/FresLtianHistoryVie.dart | 历史记录管理 |
| 我的页面 | [ ] 未开始 | lib/FrostAtelier/FresVipCenterVie.dart | 用户中心+打卡模块 |
| 用户信息编辑 | [ ] 未开始 | lib/FrostAtelier/FresVipEditVie.dart | 头像昵称编辑 |
| 收藏角色管理 | [ ] 未开始 | lib/FrostAtelier/FresFavoriteRoleVie.dart | 收藏管理 |
| 反馈页面 | [ ] 未开始 | lib/FrostAtelier/FresFebkVie.dart | 用户反馈 |
| AI角色数据模型 | [ ] 未开始 | lib/FreshCore/AiRoleModel.dart | 角色实体类 |
| 聊天消息模型 | [ ] 未开始 | lib/FreshCore/ChatMessageModel.dart | 消息实体类 |
| 用户信息模型 | [ ] 未开始 | lib/FreshCore/UserProfileModel.dart | 用户实体类 |
| 打卡记录模型 | [ ] 未开始 | lib/FreshCore/CheckInModel.dart | 打卡实体类 |
| 状态管理Store | [ ] 未开始 | lib/AromaFlow/ | MobX状态管理 |
| 数据库服务 | [ ] 未开始 | lib/PurifyMaster/ | SQLite数据服务 |
| AI聊天服务 | [ ] 未开始 | lib/ScentSage/ | Moonshot API集成 |
| UI组件库 | [ ] 未开始 | lib/IceChips/ | 通用UI组件 |
| 路由配置 | [ ] 未开始 | lib/FrostRoutes/ | 导航路由 |

## 开发计划

### 第一阶段：基础架构搭建
1. 项目结构初始化
2. MobX状态管理配置
3. SQLite数据库设计
4. 基础UI组件开发

### 第二阶段：核心功能开发
1. AI角色选择页面
2. 聊天对话功能
3. 打字机效果实现
4. 本地数据存储

### 第三阶段：用户系统完善
1. 用户信息管理
2. 打卡奖励系统
3. 聊天历史管理
4. 收藏功能

### 第四阶段：优化与测试
1. UI/UX优化
2. 性能优化
3. 错误处理完善
4. 全面测试

## 开发规范与要求

### 🚨 重要开发规则

#### 1. 命名差异化要求（关键！影响产品质量和过审）
**严禁使用常规命名**，必须使用差异化命名：
- ❌ 禁用词汇：home, history, profile, bottom, navigation, feedback, shop, store, purchase, buy, post, detail, coin, user等
- ✅ 命名策略：
  - 使用其他英文表达方式
  - 使用拼音或首字母代替
  - 可以使用"乱七八糟"的差异化名称
  - 文件名必须加上应用前缀"Fre"
  - 确保变量名仍能粗略理解含义

#### 2. 语言使用规范
- **应用显示内容**: 必须全英文，绝对不能出现中文
- **权限设置**: 不能出现中文（雷区）
- **代码注释**: 可以使用中文
- **控制台输出**: 可以使用中文

#### 3. 技术架构要求
- **状态管理**: MobX
- **设计模式**: MVC
- **动画效果**: 使用高级动画库（Lottie、Flutter Animate、Simple Animations），避免低端画板动画

#### 4. 资源管理要求
- **字体**: 需要时联网搜索并下载到本地项目文件夹
- **图片**: 联网搜索后下载到本地文件夹，或使用提供的Unsplash API
- **页面设计**: 贴合冰箱除味主题

### 📁 项目资源配置

#### AI角色图片资源
- **位置**: `/assets/ai/`
- **命名**: ai_1.png 到 ai_10.png
- **用途**: 主页角色卡片展示

#### 视频资源配置
- **位置**: `/assets/mv/`
- **现有视频**: caomao1.mp4 到 caomao3.mp4
- **预设需求**: 6-8组视频数据，复用现有3个视频地址

### 🔌 API配置

#### Unsplash API（头像图片下载）
```
Application ID: 767748
Access Key: sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM
Secret Key: b-RsiPe7hsfsVDneyWel1503jBiaKFBMqCq5dUTWvAY
```

#### Moonshot AI聊天API
```bash
curl https://api.moonshot.cn/v1/chat/completions \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR" \
    -d '{
        "model": "moonshot-v1-8k",
        "messages": [
            {"role": "system", "content": "你是专业的冰箱除味AI助手..."},
            {"role": "user", "content": "用户消息内容"}
        ],
        "temperature": 0.3
    }'
```

### 🎨 主页设计要求

#### 角色卡片信息流
- **形式**: 大图展示角色信息的个性化卡片流
- **要求**: 根据冰箱除味主题创建个性化设计，避免常规样式
- **资源**: 使用 `/assets/ai/` 中的角色图片

#### 视频流展示（备选方案）
- **形式**: 视频流形式展示
- **资源**: 使用 `/assets/mv/` 中的视频文件
- **数据预设**: 6-8组视频数据，复用现有3个视频

---

**项目创建时间**: 2025-08-01
**最后更新时间**: 2025-08-01
**开发状态**: 需求分析完成，开发规范已确立，等待风格确认后开始开发
