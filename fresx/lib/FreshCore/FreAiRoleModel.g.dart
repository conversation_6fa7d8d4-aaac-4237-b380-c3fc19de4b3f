// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreAiRoleModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreAiRoleModel _$FreAiRoleModelFromJson(Map<String, dynamic> json) =>
    FreAiRoleModel(
      roleId: json['id'] as String,
      roleName: json['name'] as String,
      shortDesc: json['short_desc'] as String,
      longDesc: json['long_desc'] as String,
      avatarPath: json['avatar_path'] as String,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      primaryColorHex: json['primary_color'] as String,
      secondaryColorHex: json['secondary_color'] as String,
      freeChats: (json['free_chats'] as num?)?.toInt() ?? 3,
      isFavorite: json['is_favorite'] as bool? ?? false,
      totalChats: (json['total_chats'] as num?)?.toInt() ?? 0,
      lastChatTime: json['last_chat_time'] == null
          ? null
          : DateTime.parse(json['last_chat_time'] as String),
    );

Map<String, dynamic> _$FreAiRoleModelToJson(FreAiRoleModel instance) =>
    <String, dynamic>{
      'id': instance.roleId,
      'name': instance.roleName,
      'short_desc': instance.shortDesc,
      'long_desc': instance.longDesc,
      'avatar_path': instance.avatarPath,
      'tags': instance.tags,
      'primary_color': instance.primaryColorHex,
      'secondary_color': instance.secondaryColorHex,
      'free_chats': instance.freeChats,
      'is_favorite': instance.isFavorite,
      'total_chats': instance.totalChats,
      'last_chat_time': instance.lastChatTime?.toIso8601String(),
    };
