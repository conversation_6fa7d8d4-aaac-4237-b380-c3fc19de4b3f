// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreAiRoleModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreAiRoleModel _$FreAiRoleModelFromJson(Map<String, dynamic> json) =>
    FreAiRoleModel(
      roleId: json['id'] as String,
      roleName: json['name'] as String,
      shortDesc: json['short_desc'] as String,
      longDesc: json['long_desc'] as String,
      avatarPath: json['avatar_path'] as String,
      tags: (json['tags'] as List<dynamic>).map((e) => e as String).toList(),
      primaryColorHex: json['primary_color'] as String,
      secondaryColorHex: json['secondary_color'] as String,
      freeChats: (json['free_chats'] as num?)?.toInt() ?? 3,
      isFavorite: json['is_favorite'] as bool? ?? false,
      totalChats: (json['total_chats'] as num?)?.toInt() ?? 0,
      lastChatTime: json['last_chat_time'] == null
          ? null
          : DateTime.parse(json['last_chat_time'] as String),
    );

Map<String, dynamic> _$FreAiRoleModelToJson(FreAiRoleModel instance) =>
    <String, dynamic>{
      'id': instance.roleId,
      'name': instance.roleName,
      'short_desc': instance.shortDesc,
      'long_desc': instance.longDesc,
      'avatar_path': instance.avatarPath,
      'tags': instance.tags,
      'primary_color': instance.primaryColorHex,
      'secondary_color': instance.secondaryColorHex,
      'free_chats': instance.freeChats,
      'is_favorite': instance.isFavorite,
      'total_chats': instance.totalChats,
      'last_chat_time': instance.lastChatTime?.toIso8601String(),
    };

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$FreAiRoleModel on FreAiRoleModelBase, Store {
  Computed<Color>? _$primaryColorComputed;

  @override
  Color get primaryColor =>
      (_$primaryColorComputed ??= Computed<Color>(() => super.primaryColor,
              name: 'FreAiRoleModelBase.primaryColor'))
          .value;
  Computed<Color>? _$secondaryColorComputed;

  @override
  Color get secondaryColor =>
      (_$secondaryColorComputed ??= Computed<Color>(() => super.secondaryColor,
              name: 'FreAiRoleModelBase.secondaryColor'))
          .value;
  Computed<bool>? _$hasFreeChatsComputed;

  @override
  bool get hasFreeChats =>
      (_$hasFreeChatsComputed ??= Computed<bool>(() => super.hasFreeChats,
              name: 'FreAiRoleModelBase.hasFreeChats'))
          .value;

  late final _$roleIdAtom =
      Atom(name: 'FreAiRoleModelBase.roleId', context: context);

  @override
  String get roleId {
    _$roleIdAtom.reportRead();
    return super.roleId;
  }

  @override
  set roleId(String value) {
    _$roleIdAtom.reportWrite(value, super.roleId, () {
      super.roleId = value;
    });
  }

  late final _$roleNameAtom =
      Atom(name: 'FreAiRoleModelBase.roleName', context: context);

  @override
  String get roleName {
    _$roleNameAtom.reportRead();
    return super.roleName;
  }

  @override
  set roleName(String value) {
    _$roleNameAtom.reportWrite(value, super.roleName, () {
      super.roleName = value;
    });
  }

  late final _$shortDescAtom =
      Atom(name: 'FreAiRoleModelBase.shortDesc', context: context);

  @override
  String get shortDesc {
    _$shortDescAtom.reportRead();
    return super.shortDesc;
  }

  @override
  set shortDesc(String value) {
    _$shortDescAtom.reportWrite(value, super.shortDesc, () {
      super.shortDesc = value;
    });
  }

  late final _$longDescAtom =
      Atom(name: 'FreAiRoleModelBase.longDesc', context: context);

  @override
  String get longDesc {
    _$longDescAtom.reportRead();
    return super.longDesc;
  }

  @override
  set longDesc(String value) {
    _$longDescAtom.reportWrite(value, super.longDesc, () {
      super.longDesc = value;
    });
  }

  late final _$avatarPathAtom =
      Atom(name: 'FreAiRoleModelBase.avatarPath', context: context);

  @override
  String get avatarPath {
    _$avatarPathAtom.reportRead();
    return super.avatarPath;
  }

  @override
  set avatarPath(String value) {
    _$avatarPathAtom.reportWrite(value, super.avatarPath, () {
      super.avatarPath = value;
    });
  }

  late final _$tagsAtom =
      Atom(name: 'FreAiRoleModelBase.tags', context: context);

  @override
  ObservableList<String> get tags {
    _$tagsAtom.reportRead();
    return super.tags;
  }

  @override
  set tags(ObservableList<String> value) {
    _$tagsAtom.reportWrite(value, super.tags, () {
      super.tags = value;
    });
  }

  late final _$primaryColorHexAtom =
      Atom(name: 'FreAiRoleModelBase.primaryColorHex', context: context);

  @override
  String get primaryColorHex {
    _$primaryColorHexAtom.reportRead();
    return super.primaryColorHex;
  }

  @override
  set primaryColorHex(String value) {
    _$primaryColorHexAtom.reportWrite(value, super.primaryColorHex, () {
      super.primaryColorHex = value;
    });
  }

  late final _$secondaryColorHexAtom =
      Atom(name: 'FreAiRoleModelBase.secondaryColorHex', context: context);

  @override
  String get secondaryColorHex {
    _$secondaryColorHexAtom.reportRead();
    return super.secondaryColorHex;
  }

  @override
  set secondaryColorHex(String value) {
    _$secondaryColorHexAtom.reportWrite(value, super.secondaryColorHex, () {
      super.secondaryColorHex = value;
    });
  }

  late final _$freeChatsAtom =
      Atom(name: 'FreAiRoleModelBase.freeChats', context: context);

  @override
  int get freeChats {
    _$freeChatsAtom.reportRead();
    return super.freeChats;
  }

  @override
  set freeChats(int value) {
    _$freeChatsAtom.reportWrite(value, super.freeChats, () {
      super.freeChats = value;
    });
  }

  late final _$isFavoriteAtom =
      Atom(name: 'FreAiRoleModelBase.isFavorite', context: context);

  @override
  bool get isFavorite {
    _$isFavoriteAtom.reportRead();
    return super.isFavorite;
  }

  @override
  set isFavorite(bool value) {
    _$isFavoriteAtom.reportWrite(value, super.isFavorite, () {
      super.isFavorite = value;
    });
  }

  late final _$totalChatsAtom =
      Atom(name: 'FreAiRoleModelBase.totalChats', context: context);

  @override
  int get totalChats {
    _$totalChatsAtom.reportRead();
    return super.totalChats;
  }

  @override
  set totalChats(int value) {
    _$totalChatsAtom.reportWrite(value, super.totalChats, () {
      super.totalChats = value;
    });
  }

  late final _$lastChatTimeAtom =
      Atom(name: 'FreAiRoleModelBase.lastChatTime', context: context);

  @override
  DateTime? get lastChatTime {
    _$lastChatTimeAtom.reportRead();
    return super.lastChatTime;
  }

  @override
  set lastChatTime(DateTime? value) {
    _$lastChatTimeAtom.reportWrite(value, super.lastChatTime, () {
      super.lastChatTime = value;
    });
  }

  late final _$FreAiRoleModelBaseActionController =
      ActionController(name: 'FreAiRoleModelBase', context: context);

  @override
  void toggleFavorite() {
    final _$actionInfo = _$FreAiRoleModelBaseActionController.startAction(
        name: 'FreAiRoleModelBase.toggleFavorite');
    try {
      return super.toggleFavorite();
    } finally {
      _$FreAiRoleModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void consumeFreeChat() {
    final _$actionInfo = _$FreAiRoleModelBaseActionController.startAction(
        name: 'FreAiRoleModelBase.consumeFreeChat');
    try {
      return super.consumeFreeChat();
    } finally {
      _$FreAiRoleModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void resetFreeChats({int count = 3}) {
    final _$actionInfo = _$FreAiRoleModelBaseActionController.startAction(
        name: 'FreAiRoleModelBase.resetFreeChats');
    try {
      return super.resetFreeChats(count: count);
    } finally {
      _$FreAiRoleModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
roleId: ${roleId},
roleName: ${roleName},
shortDesc: ${shortDesc},
longDesc: ${longDesc},
avatarPath: ${avatarPath},
tags: ${tags},
primaryColorHex: ${primaryColorHex},
secondaryColorHex: ${secondaryColorHex},
freeChats: ${freeChats},
isFavorite: ${isFavorite},
totalChats: ${totalChats},
lastChatTime: ${lastChatTime},
primaryColor: ${primaryColor},
secondaryColor: ${secondaryColor},
hasFreeChats: ${hasFreeChats}
    ''';
  }
}
