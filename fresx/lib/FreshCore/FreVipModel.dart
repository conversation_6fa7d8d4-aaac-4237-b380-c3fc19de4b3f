import 'package:mobx/mobx.dart';
import 'package:json_annotation/json_annotation.dart';

part 'FreVipModel.g.dart';

@JsonSerializable()
class FreVipModel = _FreVipModelBase with _$FreVipModel;

abstract class _FreVipModelBase with Store {
  @JsonKey(name: 'user_id')
  @observable
  String vipId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'nickname')
  @observable
  String nickname;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'avatar_path')
  @observable
  String avatarPath;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_coins')
  @observable
  int totalCoins;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_check_in')
  @observable
  DateTime? lastCheckIn;

  @J<PERSON><PERSON><PERSON>(name: 'check_in_streak')
  @observable
  int checkInStreak;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'favorite_roles')
  @observable
  ObservableList<String> favoriteRoles;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_chats')
  @observable
  int totalChats;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  @observable
  DateTime createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_active')
  @observable
  DateTime lastActive;

  _FreVipModelBase({
    required this.vipId,
    required this.nickname,
    required this.avatarPath,
    this.totalCoins = 0,
    this.lastCheckIn,
    this.checkInStreak = 0,
    required List<String> favoriteRoles,
    this.totalChats = 0,
    required this.createdAt,
    required this.lastActive,
  }) : favoriteRoles = ObservableList.of(favoriteRoles);

  // 是否今天已经打卡
  @computed
  bool get hasCheckedInToday {
    if (lastCheckIn == null) return false;
    final today = DateTime.now();
    final lastCheck = lastCheckIn!;
    return today.year == lastCheck.year &&
           today.month == lastCheck.month &&
           today.day == lastCheck.day;
  }

  // 获取今天应该获得的金币数
  @computed
  int get todayCheckInCoins {
    if (hasCheckedInToday) return 0;
    
    final nextDay = checkInStreak + 1;
    if (nextDay == 1) return 20;
    if (nextDay >= 2 && nextDay <= 3) return 50;
    if (nextDay >= 4 && nextDay <= 7) return 80;
    return 20; // 重新开始周期
  }

  // 是否可以打卡
  @computed
  bool get canCheckIn => !hasCheckedInToday;

  // 检查连续打卡是否中断
  @computed
  bool get isStreakBroken {
    if (lastCheckIn == null) return false;
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    final lastCheck = lastCheckIn!;
    
    // 如果最后打卡不是昨天，则连续中断
    return !(yesterday.year == lastCheck.year &&
             yesterday.month == lastCheck.month &&
             yesterday.day == lastCheck.day);
  }

  // 执行打卡
  @action
  FreCheckInResult performCheckIn() {
    if (hasCheckedInToday) {
      return FreCheckInResult(
        success: false,
        coinsEarned: 0,
        newStreak: checkInStreak,
        message: 'Already checked in today',
      );
    }

    // 检查是否需要重置连续天数
    if (isStreakBroken && checkInStreak > 0) {
      checkInStreak = 0;
    }

    // 计算奖励金币
    final coinsEarned = todayCheckInCoins;
    
    // 更新数据
    checkInStreak++;
    if (checkInStreak > 7) {
      checkInStreak = 1; // 重新开始7天周期
    }
    
    totalCoins += coinsEarned;
    lastCheckIn = DateTime.now();
    lastActive = DateTime.now();

    return FreCheckInResult(
      success: true,
      coinsEarned: coinsEarned,
      newStreak: checkInStreak,
      message: 'Check-in successful!',
    );
  }

  // 添加收藏角色
  @action
  void addFavoriteRole(String roleId) {
    if (!favoriteRoles.contains(roleId)) {
      favoriteRoles.add(roleId);
    }
  }

  // 移除收藏角色
  @action
  void removeFavoriteRole(String roleId) {
    favoriteRoles.remove(roleId);
  }

  // 切换收藏状态
  @action
  bool toggleFavoriteRole(String roleId) {
    if (favoriteRoles.contains(roleId)) {
      favoriteRoles.remove(roleId);
      return false;
    } else {
      favoriteRoles.add(roleId);
      return true;
    }
  }

  // 增加聊天次数
  @action
  void incrementChatCount() {
    totalChats++;
    lastActive = DateTime.now();
  }

  // 消费金币
  @action
  bool spendCoins(int amount) {
    if (totalCoins >= amount) {
      totalCoins -= amount;
      return true;
    }
    return false;
  }

  // 获得金币
  @action
  void earnCoins(int amount) {
    totalCoins += amount;
  }

  // 更新头像
  @action
  void updateAvatar(String newAvatarPath) {
    avatarPath = newAvatarPath;
    lastActive = DateTime.now();
  }

  // 更新昵称
  @action
  void updateNickname(String newNickname) {
    nickname = newNickname;
    lastActive = DateTime.now();
  }

  // JSON序列化
  factory _FreVipModelBase.fromJson(Map<String, dynamic> json) => _$FreVipModelFromJson(json);
  Map<String, dynamic> toJson() => _$FreVipModelToJson(this);

  // 创建默认用户
  static FreVipModel createDefault() {
    final now = DateTime.now();
    return FreVipModel(
      vipId: 'vip_${now.millisecondsSinceEpoch}',
      nickname: 'Fresh User',
      avatarPath: 'assets/avatars/default_avatar.png',
      totalCoins: 100, // 初始金币
      favoriteRoles: [],
      createdAt: now,
      lastActive: now,
    );
  }
}

// 打卡结果类
class FreCheckInResult {
  final bool success;
  final int coinsEarned;
  final int newStreak;
  final String message;

  FreCheckInResult({
    required this.success,
    required this.coinsEarned,
    required this.newStreak,
    required this.message,
  });
}
