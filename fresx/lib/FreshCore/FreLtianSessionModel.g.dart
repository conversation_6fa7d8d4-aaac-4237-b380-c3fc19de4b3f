// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreLtianSessionModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreLtianSessionModel _$FreLtianSessionModelFromJson(
        Map<String, dynamic> json) =>
    FreLtianSessionModel(
      sessionId: json['session_id'] as String,
      roleId: json['role_id'] as String,
      roleName: json['role_name'] as String,
      roleAvatar: json['role_avatar'] as String,
      title: json['title'] as String,
      lastMessage: json['last_message'] as String? ?? '',
      lastTime: DateTime.parse(json['last_time'] as String),
      messageCount: (json['message_count'] as num?)?.toInt() ?? 0,
      createdTime: DateTime.parse(json['created_time'] as String),
      messages: (json['messages'] as List<dynamic>?)
          ?.map((e) => FreChatMsgModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$FreLtianSessionModelToJson(
        FreLtianSessionModel instance) =>
    <String, dynamic>{
      'session_id': instance.sessionId,
      'role_id': instance.roleId,
      'role_name': instance.roleName,
      'role_avatar': instance.roleAvatar,
      'title': instance.title,
      'last_message': instance.lastMessage,
      'last_time': instance.lastTime.toIso8601String(),
      'message_count': instance.messageCount,
      'created_time': instance.createdTime.toIso8601String(),
      'messages': instance.messages,
    };
