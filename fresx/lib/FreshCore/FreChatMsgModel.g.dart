// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreChatMsgModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreChatMsgModel _$FreChatMsgModelFromJson(Map<String, dynamic> json) =>
    FreChatMsgModel(
      msgId: json['id'] as String,
      roleId: json['role_id'] as String,
      content: json['content'] as String,
      isUser: json['is_user'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: $enumDecodeNullable(_$FreMsgStatusEnumMap, json['status']) ??
          FreMsgStatus.sent,
      typingText: json['typing_text'] as String? ?? '',
      typingIndex: (json['typing_index'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FreChatMsgModelToJson(FreChatMsgModel instance) =>
    <String, dynamic>{
      'id': instance.msgId,
      'role_id': instance.roleId,
      'content': instance.content,
      'is_user': instance.isUser,
      'timestamp': instance.timestamp.toIso8601String(),
      'status': _$FreMsgStatusEnumMap[instance.status]!,
      'typing_text': instance.typingText,
      'typing_index': instance.typingIndex,
    };

const _$FreMsgStatusEnumMap = {
  FreMsgStatus.sending: 'sending',
  FreMsgStatus.sent: 'sent',
  FreMsgStatus.failed: 'failed',
  FreMsgStatus.typing: 'typing',
};

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$FreChatMsgModel on FreChatMsgModelBase, Store {
  Computed<bool>? _$isTypingComputed;

  @override
  bool get isTyping =>
      (_$isTypingComputed ??= Computed<bool>(() => super.isTyping,
              name: 'FreChatMsgModelBase.isTyping'))
          .value;
  Computed<bool>? _$isFailedComputed;

  @override
  bool get isFailed =>
      (_$isFailedComputed ??= Computed<bool>(() => super.isFailed,
              name: 'FreChatMsgModelBase.isFailed'))
          .value;
  Computed<bool>? _$isSendingComputed;

  @override
  bool get isSending =>
      (_$isSendingComputed ??= Computed<bool>(() => super.isSending,
              name: 'FreChatMsgModelBase.isSending'))
          .value;
  Computed<String>? _$displayTextComputed;

  @override
  String get displayText =>
      (_$displayTextComputed ??= Computed<String>(() => super.displayText,
              name: 'FreChatMsgModelBase.displayText'))
          .value;

  late final _$msgIdAtom =
      Atom(name: 'FreChatMsgModelBase.msgId', context: context);

  @override
  String get msgId {
    _$msgIdAtom.reportRead();
    return super.msgId;
  }

  @override
  set msgId(String value) {
    _$msgIdAtom.reportWrite(value, super.msgId, () {
      super.msgId = value;
    });
  }

  late final _$roleIdAtom =
      Atom(name: 'FreChatMsgModelBase.roleId', context: context);

  @override
  String get roleId {
    _$roleIdAtom.reportRead();
    return super.roleId;
  }

  @override
  set roleId(String value) {
    _$roleIdAtom.reportWrite(value, super.roleId, () {
      super.roleId = value;
    });
  }

  late final _$contentAtom =
      Atom(name: 'FreChatMsgModelBase.content', context: context);

  @override
  String get content {
    _$contentAtom.reportRead();
    return super.content;
  }

  @override
  set content(String value) {
    _$contentAtom.reportWrite(value, super.content, () {
      super.content = value;
    });
  }

  late final _$isUserAtom =
      Atom(name: 'FreChatMsgModelBase.isUser', context: context);

  @override
  bool get isUser {
    _$isUserAtom.reportRead();
    return super.isUser;
  }

  @override
  set isUser(bool value) {
    _$isUserAtom.reportWrite(value, super.isUser, () {
      super.isUser = value;
    });
  }

  late final _$timestampAtom =
      Atom(name: 'FreChatMsgModelBase.timestamp', context: context);

  @override
  DateTime get timestamp {
    _$timestampAtom.reportRead();
    return super.timestamp;
  }

  @override
  set timestamp(DateTime value) {
    _$timestampAtom.reportWrite(value, super.timestamp, () {
      super.timestamp = value;
    });
  }

  late final _$statusAtom =
      Atom(name: 'FreChatMsgModelBase.status', context: context);

  @override
  FreMsgStatus get status {
    _$statusAtom.reportRead();
    return super.status;
  }

  @override
  set status(FreMsgStatus value) {
    _$statusAtom.reportWrite(value, super.status, () {
      super.status = value;
    });
  }

  late final _$typingTextAtom =
      Atom(name: 'FreChatMsgModelBase.typingText', context: context);

  @override
  String get typingText {
    _$typingTextAtom.reportRead();
    return super.typingText;
  }

  @override
  set typingText(String value) {
    _$typingTextAtom.reportWrite(value, super.typingText, () {
      super.typingText = value;
    });
  }

  late final _$typingIndexAtom =
      Atom(name: 'FreChatMsgModelBase.typingIndex', context: context);

  @override
  int get typingIndex {
    _$typingIndexAtom.reportRead();
    return super.typingIndex;
  }

  @override
  set typingIndex(int value) {
    _$typingIndexAtom.reportWrite(value, super.typingIndex, () {
      super.typingIndex = value;
    });
  }

  late final _$FreChatMsgModelBaseActionController =
      ActionController(name: 'FreChatMsgModelBase', context: context);

  @override
  void startTyping() {
    final _$actionInfo = _$FreChatMsgModelBaseActionController.startAction(
        name: 'FreChatMsgModelBase.startTyping');
    try {
      return super.startTyping();
    } finally {
      _$FreChatMsgModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void updateTyping(int index) {
    final _$actionInfo = _$FreChatMsgModelBaseActionController.startAction(
        name: 'FreChatMsgModelBase.updateTyping');
    try {
      return super.updateTyping(index);
    } finally {
      _$FreChatMsgModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void completeTyping() {
    final _$actionInfo = _$FreChatMsgModelBaseActionController.startAction(
        name: 'FreChatMsgModelBase.completeTyping');
    try {
      return super.completeTyping();
    } finally {
      _$FreChatMsgModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setStatus(FreMsgStatus newStatus) {
    final _$actionInfo = _$FreChatMsgModelBaseActionController.startAction(
        name: 'FreChatMsgModelBase.setStatus');
    try {
      return super.setStatus(newStatus);
    } finally {
      _$FreChatMsgModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void retry() {
    final _$actionInfo = _$FreChatMsgModelBaseActionController.startAction(
        name: 'FreChatMsgModelBase.retry');
    try {
      return super.retry();
    } finally {
      _$FreChatMsgModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
msgId: ${msgId},
roleId: ${roleId},
content: ${content},
isUser: ${isUser},
timestamp: ${timestamp},
status: ${status},
typingText: ${typingText},
typingIndex: ${typingIndex},
isTyping: ${isTyping},
isFailed: ${isFailed},
isSending: ${isSending},
displayText: ${displayText}
    ''';
  }
}
