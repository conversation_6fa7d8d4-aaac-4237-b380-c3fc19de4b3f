// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreChatMsgModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreChatMsgModel _$FreChatMsgModelFromJson(Map<String, dynamic> json) =>
    FreChatMsgModel(
      msgId: json['id'] as String,
      roleId: json['role_id'] as String,
      content: json['content'] as String,
      isUser: json['is_user'] as bool,
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: $enumDecodeNullable(_$FreMsgStatusEnumMap, json['status']) ??
          FreMsgStatus.sent,
      typingText: json['typing_text'] as String? ?? '',
      typingIndex: (json['typing_index'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$FreChatMsgModelToJson(FreChatMsgModel instance) =>
    <String, dynamic>{
      'id': instance.msgId,
      'role_id': instance.roleId,
      'content': instance.content,
      'is_user': instance.isUser,
      'timestamp': instance.timestamp.toIso8601String(),
      'status': _$FreMsgStatusEnumMap[instance.status]!,
      'typing_text': instance.typingText,
      'typing_index': instance.typingIndex,
    };

const _$FreMsgStatusEnumMap = {
  FreMsgStatus.sending: 'sending',
  FreMsgStatus.sent: 'sent',
  FreMsgStatus.failed: 'failed',
  FreMsgStatus.typing: 'typing',
};
