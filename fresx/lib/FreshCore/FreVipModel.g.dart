// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreVipModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreVipModel _$FreVipModelFromJson(Map<String, dynamic> json) => FreVipModel(
      vipId: json['user_id'] as String,
      nickname: json['nickname'] as String,
      avatarPath: json['avatar_path'] as String,
      totalCoins: (json['total_coins'] as num?)?.toInt() ?? 0,
      lastCheckIn: json['last_check_in'] == null
          ? null
          : DateTime.parse(json['last_check_in'] as String),
      checkInStreak: (json['check_in_streak'] as num?)?.toInt() ?? 0,
      favoriteRoles: (json['favorite_roles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalChats: (json['total_chats'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastActive: DateTime.parse(json['last_active'] as String),
    );

Map<String, dynamic> _$FreVipModelToJson(FreVipModel instance) =>
    <String, dynamic>{
      'user_id': instance.vipId,
      'nickname': instance.nickname,
      'avatar_path': instance.avatarPath,
      'total_coins': instance.totalCoins,
      'last_check_in': instance.lastCheckIn?.toIso8601String(),
      'check_in_streak': instance.checkInStreak,
      'favorite_roles': instance.favoriteRoles,
      'total_chats': instance.totalChats,
      'created_at': instance.createdAt.toIso8601String(),
      'last_active': instance.lastActive.toIso8601String(),
    };
