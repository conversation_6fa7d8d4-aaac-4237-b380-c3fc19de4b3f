// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreVipModel.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FreVipModel _$FreVipModelFromJson(Map<String, dynamic> json) => FreVipModel(
      vipId: json['user_id'] as String,
      nickname: json['nickname'] as String,
      avatarPath: json['avatar_path'] as String,
      totalCoins: (json['total_coins'] as num?)?.toInt() ?? 0,
      lastCheckIn: json['last_check_in'] == null
          ? null
          : DateTime.parse(json['last_check_in'] as String),
      checkInStreak: (json['check_in_streak'] as num?)?.toInt() ?? 0,
      favoriteRoles: (json['favorite_roles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      totalChats: (json['total_chats'] as num?)?.toInt() ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastActive: DateTime.parse(json['last_active'] as String),
    );

Map<String, dynamic> _$FreVipModelToJson(FreVipModel instance) =>
    <String, dynamic>{
      'user_id': instance.vipId,
      'nickname': instance.nickname,
      'avatar_path': instance.avatarPath,
      'total_coins': instance.totalCoins,
      'last_check_in': instance.lastCheckIn?.toIso8601String(),
      'check_in_streak': instance.checkInStreak,
      'favorite_roles': instance.favoriteRoles,
      'total_chats': instance.totalChats,
      'created_at': instance.createdAt.toIso8601String(),
      'last_active': instance.lastActive.toIso8601String(),
    };

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$FreVipModel on FreVipModelBase, Store {
  Computed<bool>? _$hasCheckedInTodayComputed;

  @override
  bool get hasCheckedInToday => (_$hasCheckedInTodayComputed ??= Computed<bool>(
          () => super.hasCheckedInToday,
          name: 'FreVipModelBase.hasCheckedInToday'))
      .value;
  Computed<int>? _$todayCheckInCoinsComputed;

  @override
  int get todayCheckInCoins => (_$todayCheckInCoinsComputed ??= Computed<int>(
          () => super.todayCheckInCoins,
          name: 'FreVipModelBase.todayCheckInCoins'))
      .value;
  Computed<bool>? _$canCheckInComputed;

  @override
  bool get canCheckIn =>
      (_$canCheckInComputed ??= Computed<bool>(() => super.canCheckIn,
              name: 'FreVipModelBase.canCheckIn'))
          .value;
  Computed<bool>? _$isStreakBrokenComputed;

  @override
  bool get isStreakBroken =>
      (_$isStreakBrokenComputed ??= Computed<bool>(() => super.isStreakBroken,
              name: 'FreVipModelBase.isStreakBroken'))
          .value;

  late final _$vipIdAtom =
      Atom(name: 'FreVipModelBase.vipId', context: context);

  @override
  String get vipId {
    _$vipIdAtom.reportRead();
    return super.vipId;
  }

  @override
  set vipId(String value) {
    _$vipIdAtom.reportWrite(value, super.vipId, () {
      super.vipId = value;
    });
  }

  late final _$nicknameAtom =
      Atom(name: 'FreVipModelBase.nickname', context: context);

  @override
  String get nickname {
    _$nicknameAtom.reportRead();
    return super.nickname;
  }

  @override
  set nickname(String value) {
    _$nicknameAtom.reportWrite(value, super.nickname, () {
      super.nickname = value;
    });
  }

  late final _$avatarPathAtom =
      Atom(name: 'FreVipModelBase.avatarPath', context: context);

  @override
  String get avatarPath {
    _$avatarPathAtom.reportRead();
    return super.avatarPath;
  }

  @override
  set avatarPath(String value) {
    _$avatarPathAtom.reportWrite(value, super.avatarPath, () {
      super.avatarPath = value;
    });
  }

  late final _$totalCoinsAtom =
      Atom(name: 'FreVipModelBase.totalCoins', context: context);

  @override
  int get totalCoins {
    _$totalCoinsAtom.reportRead();
    return super.totalCoins;
  }

  @override
  set totalCoins(int value) {
    _$totalCoinsAtom.reportWrite(value, super.totalCoins, () {
      super.totalCoins = value;
    });
  }

  late final _$lastCheckInAtom =
      Atom(name: 'FreVipModelBase.lastCheckIn', context: context);

  @override
  DateTime? get lastCheckIn {
    _$lastCheckInAtom.reportRead();
    return super.lastCheckIn;
  }

  @override
  set lastCheckIn(DateTime? value) {
    _$lastCheckInAtom.reportWrite(value, super.lastCheckIn, () {
      super.lastCheckIn = value;
    });
  }

  late final _$checkInStreakAtom =
      Atom(name: 'FreVipModelBase.checkInStreak', context: context);

  @override
  int get checkInStreak {
    _$checkInStreakAtom.reportRead();
    return super.checkInStreak;
  }

  @override
  set checkInStreak(int value) {
    _$checkInStreakAtom.reportWrite(value, super.checkInStreak, () {
      super.checkInStreak = value;
    });
  }

  late final _$favoriteRolesAtom =
      Atom(name: 'FreVipModelBase.favoriteRoles', context: context);

  @override
  ObservableList<String> get favoriteRoles {
    _$favoriteRolesAtom.reportRead();
    return super.favoriteRoles;
  }

  @override
  set favoriteRoles(ObservableList<String> value) {
    _$favoriteRolesAtom.reportWrite(value, super.favoriteRoles, () {
      super.favoriteRoles = value;
    });
  }

  late final _$totalChatsAtom =
      Atom(name: 'FreVipModelBase.totalChats', context: context);

  @override
  int get totalChats {
    _$totalChatsAtom.reportRead();
    return super.totalChats;
  }

  @override
  set totalChats(int value) {
    _$totalChatsAtom.reportWrite(value, super.totalChats, () {
      super.totalChats = value;
    });
  }

  late final _$createdAtAtom =
      Atom(name: 'FreVipModelBase.createdAt', context: context);

  @override
  DateTime get createdAt {
    _$createdAtAtom.reportRead();
    return super.createdAt;
  }

  @override
  set createdAt(DateTime value) {
    _$createdAtAtom.reportWrite(value, super.createdAt, () {
      super.createdAt = value;
    });
  }

  late final _$lastActiveAtom =
      Atom(name: 'FreVipModelBase.lastActive', context: context);

  @override
  DateTime get lastActive {
    _$lastActiveAtom.reportRead();
    return super.lastActive;
  }

  @override
  set lastActive(DateTime value) {
    _$lastActiveAtom.reportWrite(value, super.lastActive, () {
      super.lastActive = value;
    });
  }

  late final _$FreVipModelBaseActionController =
      ActionController(name: 'FreVipModelBase', context: context);

  @override
  FreCheckInResult performCheckIn() {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.performCheckIn');
    try {
      return super.performCheckIn();
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void addFavoriteRole(String roleId) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.addFavoriteRole');
    try {
      return super.addFavoriteRole(roleId);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void removeFavoriteRole(String roleId) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.removeFavoriteRole');
    try {
      return super.removeFavoriteRole(roleId);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  bool toggleFavoriteRole(String roleId) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.toggleFavoriteRole');
    try {
      return super.toggleFavoriteRole(roleId);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void incrementChatCount() {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.incrementChatCount');
    try {
      return super.incrementChatCount();
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  bool spendCoins(int amount) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.spendCoins');
    try {
      return super.spendCoins(amount);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void earnCoins(int amount) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.earnCoins');
    try {
      return super.earnCoins(amount);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void updateAvatar(String newAvatarPath) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.updateAvatar');
    try {
      return super.updateAvatar(newAvatarPath);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void updateNickname(String newNickname) {
    final _$actionInfo = _$FreVipModelBaseActionController.startAction(
        name: 'FreVipModelBase.updateNickname');
    try {
      return super.updateNickname(newNickname);
    } finally {
      _$FreVipModelBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
vipId: ${vipId},
nickname: ${nickname},
avatarPath: ${avatarPath},
totalCoins: ${totalCoins},
lastCheckIn: ${lastCheckIn},
checkInStreak: ${checkInStreak},
favoriteRoles: ${favoriteRoles},
totalChats: ${totalChats},
createdAt: ${createdAt},
lastActive: ${lastActive},
hasCheckedInToday: ${hasCheckedInToday},
todayCheckInCoins: ${todayCheckInCoins},
canCheckIn: ${canCheckIn},
isStreakBroken: ${isStreakBroken}
    ''';
  }
}
