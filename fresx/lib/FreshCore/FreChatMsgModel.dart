import 'package:mobx/mobx.dart';
import 'package:json_annotation/json_annotation.dart';

part 'FreChatMsgModel.g.dart';

enum FreMsgStatus {
  sending,
  sent,
  failed,
  typing
}

@JsonSerializable()
class FreChatMsgModel with Store {
  @JsonKey(name: 'id')
  @observable
  String msgId;

  @Json<PERSON><PERSON>(name: 'role_id')
  @observable
  String roleId;

  @JsonKey(name: 'content')
  @observable
  String content;

  @<PERSON>son<PERSON>ey(name: 'is_user')
  @observable
  bool isUser;

  @<PERSON>son<PERSON>ey(name: 'timestamp')
  @observable
  DateTime timestamp;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  @observable
  FreMsgStatus status;

  @<PERSON>son<PERSON><PERSON>(name: 'typing_text')
  @observable
  String typingText;

  @<PERSON>sonKey(name: 'typing_index')
  @observable
  int typingIndex;

  FreChatMsgModel({
    required this.msgId,
    required this.roleId,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.status = FreMsgStatus.sent,
    this.typingText = '',
    this.typingIndex = 0,
  });

  // 是否正在打字
  @computed
  bool get isTyping => status == FreMsgStatus.typing;

  // 是否发送失败
  @computed
  bool get isFailed => status == FreMsgStatus.failed;

  // 是否发送中
  @computed
  bool get isSending => status == FreMsgStatus.sending;

  // 获取显示的文本（用于打字机效果）
  @computed
  String get displayText {
    if (isTyping && typingIndex < content.length) {
      return content.substring(0, typingIndex);
    }
    return content;
  }

  // 开始打字机效果
  @action
  void startTyping() {
    status = FreMsgStatus.typing;
    typingIndex = 0;
    typingText = '';
  }

  // 更新打字进度
  @action
  void updateTyping(int index) {
    if (index <= content.length) {
      typingIndex = index;
      typingText = content.substring(0, index);
      
      if (index >= content.length) {
        status = FreMsgStatus.sent;
      }
    }
  }

  // 完成打字
  @action
  void completeTyping() {
    typingIndex = content.length;
    typingText = content;
    status = FreMsgStatus.sent;
  }

  // 设置发送状态
  @action
  void setStatus(FreMsgStatus newStatus) {
    status = newStatus;
  }

  // 重试发送
  @action
  void retry() {
    status = FreMsgStatus.sending;
  }

  // JSON序列化
  factory FreChatMsgModel.fromJson(Map<String, dynamic> json) => _$FreChatMsgModelFromJson(json);
  Map<String, dynamic> toJson() => _$FreChatMsgModelToJson(this);

  // 创建用户消息
  static FreChatMsgModel createUserMessage({
    required String roleId,
    required String content,
  }) {
    return FreChatMsgModel(
      msgId: 'user_${DateTime.now().millisecondsSinceEpoch}',
      roleId: roleId,
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
      status: FreMsgStatus.sent,
    );
  }

  // 创建AI消息
  static FreChatMsgModel createAiMessage({
    required String roleId,
    required String content,
  }) {
    return FreChatMsgModel(
      msgId: 'ai_${DateTime.now().millisecondsSinceEpoch}',
      roleId: roleId,
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      status: FreMsgStatus.typing,
    );
  }

  // 创建系统消息
  static FreChatMsgModel createSystemMessage({
    required String roleId,
    required String content,
  }) {
    return FreChatMsgModel(
      msgId: 'sys_${DateTime.now().millisecondsSinceEpoch}',
      roleId: roleId,
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      status: FreMsgStatus.sent,
    );
  }
}
