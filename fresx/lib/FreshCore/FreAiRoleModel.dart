import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';
import 'package:json_annotation/json_annotation.dart';

part 'FreAiRoleModel.g.dart';

@JsonSerializable()
class FreAiRoleModel = _FreAiRoleModelBase with _$FreAiRoleModel;

abstract class _FreAiRoleModelBase with Store {
  @Json<PERSON>ey(name: 'id')
  @observable
  String roleId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  @observable
  String roleName;

  @Json<PERSON><PERSON>(name: 'short_desc')
  @observable
  String shortDesc;

  @Json<PERSON><PERSON>(name: 'long_desc')
  @observable
  String longDesc;

  @Json<PERSON><PERSON>(name: 'avatar_path')
  @observable
  String avatarPath;

  @JsonKey(name: 'tags')
  @observable
  ObservableList<String> tags;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'primary_color')
  @observable
  String primaryColorHex;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'secondary_color')
  @observable
  String secondaryColorHex;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'free_chats')
  @observable
  int freeChats;

  @Json<PERSON><PERSON>(name: 'is_favorite')
  @observable
  bool isFavorite;

  @JsonKey(name: 'total_chats')
  @observable
  int totalChats;

  @JsonKey(name: 'last_chat_time')
  @observable
  DateTime? lastChatTime;

  _FreAiRoleModelBase({
    required this.roleId,
    required this.roleName,
    required this.shortDesc,
    required this.longDesc,
    required this.avatarPath,
    required List<String> tags,
    required this.primaryColorHex,
    required this.secondaryColorHex,
    this.freeChats = 3,
    this.isFavorite = false,
    this.totalChats = 0,
    this.lastChatTime,
  }) : tags = ObservableList.of(tags);

  // 获取主色调
  @computed
  Color get primaryColor => Color(int.parse(primaryColorHex.replaceFirst('#', '0xFF')));

  // 获取辅助色
  @computed
  Color get secondaryColor => Color(int.parse(secondaryColorHex.replaceFirst('#', '0xFF')));

  // 是否还有免费聊天次数
  @computed
  bool get hasFreeChats => freeChats > 0;

  // 切换收藏状态
  @action
  void toggleFavorite() {
    isFavorite = !isFavorite;
  }

  // 消耗一次免费聊天
  @action
  void consumeFreeChat() {
    if (freeChats > 0) {
      freeChats--;
      totalChats++;
      lastChatTime = DateTime.now();
    }
  }

  // 重置免费聊天次数（可用于购买或活动奖励）
  @action
  void resetFreeChats({int count = 3}) {
    freeChats = count;
  }

  // JSON序列化
  factory _FreAiRoleModelBase.fromJson(Map<String, dynamic> json) => _$FreAiRoleModelFromJson(json);
  Map<String, dynamic> toJson() => _$FreAiRoleModelToJson(this);

  // 创建默认AI角色数据
  static List<FreAiRoleModel> createDefaultRoles() {
    return [
      FreAiRoleModel(
        roleId: 'veggie_fresh_01',
        roleName: 'VeggieFresh',
        shortDesc: 'Teach removing fridge odors from fruits and veggies.',
        longDesc: 'Store produce in breathable bags, wipe spoiled items promptly. Place a bowl of citrus peels in the crisper to neutralize smells, replace weekly.',
        avatarPath: 'assets/freai/ai1.png',
        tags: ['Fruits', 'Vegetables', 'Natural'],
        primaryColorHex: '#F7DC6F',
        secondaryColorHex: '#2ECC71',
      ),
      FreAiRoleModel(
        roleId: 'meat_odor_fix_02',
        roleName: 'MeatOdorFix',
        shortDesc: 'Guide eliminating fridge odors from meat and seafood.',
        longDesc: 'Seal raw meat in airtight containers, clean spills immediately with vinegar. Place a small dish of activated charcoal near the meat drawer to absorb odors.',
        avatarPath: 'assets/freai/ai2.png',
        tags: ['Meat', 'Seafood', 'Charcoal'],
        primaryColorHex: '#888888',
        secondaryColorHex: '#F39C12',
      ),
      FreAiRoleModel(
        roleId: 'dairy_fresh_03',
        roleName: 'DairyFresh',
        shortDesc: 'Instruct removing fridge smells from milk, cheese, etc.',
        longDesc: 'Tightly seal dairy containers, check expiration dates often. Wipe shelves with baking soda solution if spills occur, leave a lemon wedge on the door shelf.',
        avatarPath: 'assets/freai/ai3.png',
        tags: ['Dairy', 'Milk', 'Cheese'],
        primaryColorHex: '#3498DB',
        secondaryColorHex: '#F7DC6F',
      ),
      FreAiRoleModel(
        roleId: 'cooked_food_fresh_04',
        roleName: 'CookedFoodFresh',
        shortDesc: 'Provide tips to eliminate odors from leftover meals.',
        longDesc: 'Store leftovers in lidded glass containers, label with dates. Freeze excess to prevent spoilage, wipe containers with lemon juice before reuse.',
        avatarPath: 'assets/freai/ai4.png',
        tags: ['Leftovers', 'Cooked', 'Storage'],
        primaryColorHex: '#F39C12',
        secondaryColorHex: '#F5F5F5',
      ),
      FreAiRoleModel(
        roleId: 'corner_fresh_05',
        roleName: 'CornerFresh',
        shortDesc: 'Teach cleaning hidden fridge spots to remove odors.',
        longDesc: 'Pull out drawers monthly, scrub with soapy water + baking soda. Use a small brush to clean gasket crevices, dry thoroughly to prevent mold smells.',
        avatarPath: 'assets/freai/ai5.png',
        tags: ['Cleaning', 'Hidden', 'Deep'],
        primaryColorHex: '#F5F5F5',
        secondaryColorHex: '#2ECC71',
      ),
      FreAiRoleModel(
        roleId: 'natural_deodor_06',
        roleName: 'NaturalDeodor',
        shortDesc: 'Guide making homemade fridge odor absorbers.',
        longDesc: 'Fill a jar with baking soda + a few drops of lemon oil, punch holes in the lid. Or use a bowl of oats—renew every 2 weeks for continuous freshness.',
        avatarPath: 'assets/freai/ai6.png',
        tags: ['Natural', 'DIY', 'Homemade'],
        primaryColorHex: '#2ECC71',
        secondaryColorHex: '#F7DC6F',
      ),
      FreAiRoleModel(
        roleId: 'deep_clean_fresh_07',
        roleName: 'DeepCleanFresh',
        shortDesc: 'Instruct thorough fridge cleaning to banish odors.',
        longDesc: 'Empty fridge, turn off, wipe all surfaces with vinegar. Leave doors open 2 hours, place a bowl of coffee grounds inside before restocking.',
        avatarPath: 'assets/freai/ai7.png',
        tags: ['Deep Clean', 'Thorough', 'Reset'],
        primaryColorHex: '#888888',
        secondaryColorHex: '#2ECC71',
      ),
      FreAiRoleModel(
        roleId: 'odor_stop_08',
        roleName: 'OdorStop',
        shortDesc: 'Offer tips to prevent fridge odors from forming.',
        longDesc: 'Check food weekly, discard expired items. Use separate containers for strong-smelling foods (onions, garlic), keep a deodorizer in place year-round.',
        avatarPath: 'assets/freai/ai8.png',
        tags: ['Prevention', 'Maintenance', 'Tips'],
        primaryColorHex: '#F5F5F5',
        secondaryColorHex: '#2ECC71',
      ),
      FreAiRoleModel(
        roleId: 'mini_fridge_fresh_09',
        roleName: 'MiniFridgeFresh',
        shortDesc: 'Teach removing odors from compact fridges.',
        longDesc: 'Use a small sachet of baking soda, replace monthly. Wipe interior with diluted bleach (rinsed well) for tough smells, avoid overpacking to let air circulate.',
        avatarPath: 'assets/freai/ai9.png',
        tags: ['Mini', 'Compact', 'Small'],
        primaryColorHex: '#888888',
        secondaryColorHex: '#F7DC6F',
      ),
      FreAiRoleModel(
        roleId: 'long_fresh_10',
        roleName: 'LongFresh',
        shortDesc: 'Provide ways to keep fridges smelling fresh long-term.',
        longDesc: 'Rotate deodorizers (baking soda, charcoal, citrus) monthly. Line drawers with baking soda-soaked paper towels, replace when they harden.',
        avatarPath: 'assets/freai/ai10.png',
        tags: ['Long-term', 'Maintenance', 'Rotation'],
        primaryColorHex: '#2ECC71',
        secondaryColorHex: '#F39C12',
      ),
    ];
  }
}
