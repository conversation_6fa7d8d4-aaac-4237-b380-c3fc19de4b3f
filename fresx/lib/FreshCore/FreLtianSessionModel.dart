import 'package:mobx/mobx.dart';
import 'package:json_annotation/json_annotation.dart';
import 'FreChatMsgModel.dart';
import 'FreAiRoleModel.dart';

part 'FreLtianSessionModel.g.dart';

/// 聊天会话模型
@JsonSerializable()
class FreLtianSessionModel with Store {
  @JsonKey(name: 'session_id')
  @observable
  String sessionId;

  @JsonKey(name: 'role_id')
  @observable
  String roleId;

  @Json<PERSON>ey(name: 'role_name')
  @observable
  String roleName;

  @Json<PERSON>ey(name: 'role_avatar')
  @observable
  String roleAvatar;

  @Json<PERSON><PERSON>(name: 'title')
  @observable
  String title;

  @Json<PERSON><PERSON>(name: 'last_message')
  @observable
  String lastMessage;

  @Json<PERSON><PERSON>(name: 'last_time')
  @observable
  DateTime lastTime;

  @Json<PERSON>ey(name: 'message_count')
  @observable
  int messageCount;

  @<PERSON>son<PERSON><PERSON>(name: 'created_time')
  @observable
  DateTime createdTime;

  @JsonKey(name: 'messages')
  @observable
  ObservableList<FreChatMsgModel> messages;

  FreLtianSessionModel({
    required this.sessionId,
    required this.roleId,
    required this.roleName,
    required this.roleAvatar,
    required this.title,
    this.lastMessage = '',
    required this.lastTime,
    this.messageCount = 0,
    required this.createdTime,
    List<FreChatMsgModel>? messages,
  }) : messages = ObservableList.of(messages ?? []);

  /// 添加消息
  @action
  void addMessage(FreChatMsgModel message) {
    messages.add(message);
    lastMessage = message.content;
    lastTime = message.timestamp;
    messageCount = messages.length;
    
    // 如果是第一条用户消息，使用它作为标题
    if (title.isEmpty && message.isUser && message.content.isNotEmpty) {
      title = message.content.length > 20 
          ? '${message.content.substring(0, 20)}...'
          : message.content;
    }
  }

  /// 更新最后一条消息
  @action
  void updateLastMessage(String content) {
    lastMessage = content;
    lastTime = DateTime.now();
  }

  /// 获取格式化的最后活动时间
  @computed
  String get formattedLastTime {
    final now = DateTime.now();
    final diff = now.difference(lastTime);
    
    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}h ago';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}d ago';
    } else {
      return '${lastTime.month}/${lastTime.day}';
    }
  }

  /// 获取用户消息数量
  @computed
  int get userMessageCount {
    return messages.where((msg) => msg.isUser).length;
  }

  /// 获取AI消息数量
  @computed
  int get aiMessageCount {
    return messages.where((msg) => !msg.isUser).length;
  }

  /// JSON序列化
  factory FreLtianSessionModel.fromJson(Map<String, dynamic> json) => _$FreLtianSessionModelFromJson(json);
  Map<String, dynamic> toJson() => _$FreLtianSessionModelToJson(this);

  /// 从AI角色创建新会话
  static FreLtianSessionModel createFromRole(FreAiRoleModel role) {
    final now = DateTime.now();
    return FreLtianSessionModel(
      sessionId: 'session_${role.roleId}_${now.millisecondsSinceEpoch}',
      roleId: role.roleId,
      roleName: role.roleName,
      roleAvatar: role.avatarPath,
      title: '',
      lastTime: now,
      createdTime: now,
    );
  }

  /// 创建欢迎消息
  FreChatMsgModel createWelcomeMessage() {
    return FreChatMsgModel.createSystemMessage(
      roleId: roleId,
      content: 'Hello! I\'m $roleName. How can I help you today?',
    );
  }

  /// 清空消息
  @action
  void clearMessages() {
    messages.clear();
    messageCount = 0;
    lastMessage = '';
  }

  /// 删除指定消息
  @action
  void removeMessage(String messageId) {
    messages.removeWhere((msg) => msg.msgId == messageId);
    messageCount = messages.length;
    
    if (messages.isNotEmpty) {
      final lastMsg = messages.last;
      lastMessage = lastMsg.content;
      lastTime = lastMsg.timestamp;
    } else {
      lastMessage = '';
    }
  }

  /// 获取消息历史（用于API调用）
  List<Map<String, String>> getMessageHistory({int? limit}) {
    var msgList = messages.where((msg) => msg.status == FreMsgStatus.sent).toList();
    
    if (limit != null && msgList.length > limit) {
      msgList = msgList.sublist(msgList.length - limit);
    }
    
    return msgList.map((msg) => {
      'role': msg.isUser ? 'user' : 'assistant',
      'content': msg.content,
    }).toList();
  }
}
