import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../AromaFlow/FreAiRoleStore.dart';
import '../IceChips/FreNeumorphicContainer.dart';
import '../IceChips/FreAiRoleCard.dart';
import '../FreshCore/FreAiRoleModel.dart';
import '../IceChips/FreVipService.dart';
import '../IceChips/FreVipModel.dart';

class FreAiRoleSelectVie extends StatefulWidget {
  const FreAiRoleSelectVie({Key? key}) : super(key: key);

  @override
  State<FreAiRoleSelectVie> createState() => _FreAiRoleSelectVieState();
}

class _FreAiRoleSelectVieState extends State<FreAiRoleSelectVie>
    with TickerProviderStateMixin {
  late FreAiRoleStore _roleStore;
  late ScrollController _scrollController;
  late AnimationController _headerAnimationController;
  late Animation<double> _headerAnimation;
  final FreVipService _vipService = FreVipService.instance;
  FreVipModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _roleStore = FreAiRoleStore();
    _scrollController = ScrollController();
    
    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _initializeData();
    _loadUserData();
  }

  void _loadUserData() {
    setState(() {
      _currentUser = _vipService.currentUser;
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _headerAnimationController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    await _roleStore.initializeRoles();
    _headerAnimationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F0F3),
      body: SafeArea(
        child: Observer(
          builder: (context) {
            if (_roleStore.isLoading) {
              return _buildLoadingView();
            }

            if (_roleStore.errorMessage != null) {
              return _buildErrorView();
            }

            return CustomScrollView(
              controller: _scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                // 自定义头部
                _buildSliverHeader(),
                
                // 搜索和过滤区域
                _buildSearchSection(),
                
                // 标签过滤区域
                _buildTagsSection(),
                
                // AI角色卡片网格
                _buildRoleCardsGrid(),
                
                // 底部间距
                const SliverToBoxAdapter(
                  child: SizedBox(height: 100),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2ECC71)),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Color(0xFFE74C3C),
          ),
          const SizedBox(height: 16),
          Text(
            _roleStore.errorMessage ?? 'Unknown error',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FreNeumorphicButton(
            onPressed: _initializeData,
            child: const Text(
              'Retry',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSliverHeader() {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: _headerAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, 50 * (1 - _headerAnimation.value)),
            child: Opacity(
              opacity: _headerAnimation.value,
              child: Container(
                padding: const EdgeInsets.fromLTRB(24, 20, 24, 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 应用标题
                    Row(
                      children: [
                        FreNeumorphicContainer(
                          width: 48,
                          height: 48,
                          borderRadius: BorderRadius.circular(24),
                          backgroundColor: const Color(0xFF2ECC71),
                          child: const Icon(
                            Icons.ac_unit,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Fresx',
                                style: TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF333333),
                                  letterSpacing: 1.2,
                                ),
                              ),
                              Text(
                                'AI Fridge Odor Fixers',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF888888),
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // 用户头像
                        _buildUserAvatar(),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // 欢迎文本
                    const Text(
                      'Choose Your AI Expert',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF333333),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    const Text(
                      'Get personalized solutions for every fridge odor problem',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF666666),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchSection() {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: FreNeumorphicContainer(
          width: double.infinity,
          height: 56,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          borderRadius: BorderRadius.circular(28),
          isInset: true,
          child: Row(
            children: [
              const Icon(
                Icons.search,
                color: Color(0xFF888888),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: TextField(
                  onChanged: _roleStore.setSearchQuery,
                  decoration: const InputDecoration(
                    hintText: 'Search AI experts...',
                    hintStyle: TextStyle(
                      color: Color(0xFF888888),
                      fontSize: 16,
                    ),
                    border: InputBorder.none,
                  ),
                  style: const TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 16,
                  ),
                ),
              ),
              if (_roleStore.searchQuery.isNotEmpty)
                GestureDetector(
                  onTap: () => _roleStore.setSearchQuery(''),
                  child: const Icon(
                    Icons.clear,
                    color: Color(0xFF888888),
                    size: 20,
                  ),
                ),
            ],
          ),
        ),
      ).animate()
        .fadeIn(delay: 200.ms, duration: 600.ms)
        .slideX(begin: -0.2, end: 0, duration: 600.ms),
    );
  }

  Widget _buildTagsSection() {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 24),
          itemCount: _roleStore.availableTags.length + 1,
          itemBuilder: (context, index) {
            if (index == 0) {
              return _buildTagChip('All', _roleStore.selectedTag.isEmpty);
            }
            
            final tag = _roleStore.availableTags[index - 1];
            final isSelected = _roleStore.selectedTag == tag;
            
            return _buildTagChip(tag, isSelected);
          },
        ),
      ).animate()
        .fadeIn(delay: 400.ms, duration: 600.ms)
        .slideY(begin: 0.3, end: 0, duration: 600.ms),
    );
  }

  Widget _buildTagChip(String tag, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      child: FreNeumorphicContainer(
        width: 80,
        height: 36,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        borderRadius: BorderRadius.circular(18),
        backgroundColor: isSelected
          ? const Color(0xFF2ECC71)
          : const Color(0xFFF0F0F3),
        isInset: !isSelected,
        onTap: () {
          if (tag == 'All') {
            _roleStore.setSelectedTag('');
          } else {
            _roleStore.setSelectedTag(isSelected ? '' : tag);
          }
        },
        child: Text(
          tag,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: isSelected
              ? Colors.white
              : const Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  Widget _buildRoleCardsGrid() {
    final filteredRoles = _roleStore.filteredRoles;
    
    if (filteredRoles.isEmpty) {
      return SliverToBoxAdapter(
        child: Container(
          padding: const EdgeInsets.all(48),
          child: Column(
            children: [
              const Icon(
                Icons.search_off,
                size: 64,
                color: Color(0xFF888888),
              ),
              const SizedBox(height: 16),
              const Text(
                'No AI experts found',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Try adjusting your search or filters',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF888888),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final role = filteredRoles[index];
            return FreAiRoleCard(
              aiRole: role,
              onTap: () => _handleRoleSelection(role),
              onFavorite: () => _roleStore.toggleRoleFavorite(role.roleId),
            );
          },
          childCount: filteredRoles.length,
        ),
      ),
    );
  }

  void _handleRoleSelection(FreAiRoleModel role) {
    _roleStore.selectRole(role);

    // 导航到聊天页面
    Navigator.pushNamed(
      context,
      '/chat',
      arguments: role,
    );
  }

  Widget _buildUserAvatar() {
    if (_currentUser == null) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.grey.withOpacity(0.3),
        ),
        child: const Icon(
          CupertinoIcons.person,
          color: Colors.grey,
          size: 20,
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, '/profile');
      },
      child: Hero(
        tag: 'main_user_avatar',
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: _currentUser!.levelColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: _currentUser!.levelColor,
              width: 2,
            ),
          ),
          child: ClipOval(
            child: Image.asset(
              _currentUser!.avatarPath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: _currentUser!.levelColor.withOpacity(0.2),
                  child: Icon(
                    CupertinoIcons.person_fill,
                    size: 20,
                    color: _currentUser!.levelColor,
                  ),
                );
              },
            ),
          ),
        ),
      ),
    ).animate()
      .fadeIn(delay: 800.ms, duration: 600.ms)
      .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0));
  }
}
