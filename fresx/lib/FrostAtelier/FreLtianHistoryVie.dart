import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../FreshCore/FreLtianSessionModel.dart';
import '../FreshCore/FreAiRoleModel.dart';
import '../IceChips/FreNeumorphicContainer.dart';
import '../IceChips/FreLtianService.dart';
import 'FreLtianDialogVie.dart';

/// 聊天历史记录页面
class FreLtianHistoryVie extends StatefulWidget {
  const FreLtianHistoryVie({Key? key}) : super(key: key);

  @override
  State<FreLtianHistoryVie> createState() => _FreLtianHistoryVieState();
}

class _FreLtianHistoryVieState extends State<FreLtianHistoryVie>
    with TickerProviderStateMixin {
  final FreLtianService _ltianService = FreLtianService.instance;
  late AnimationController _refreshController;
  late Animation<double> _refreshAnimation;
  
  List<FreLtianSessionModel> _sessions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _refreshAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _refreshController,
      curve: Curves.easeInOut,
    ));
    
    _loadSessions();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _loadSessions() async {
    setState(() {
      _isLoading = true;
    });
    
    await _ltianService.initialize();
    
    setState(() {
      _sessions = _ltianService.sessions;
      _isLoading = false;
    });
  }

  Future<void> _refreshSessions() async {
    _refreshController.forward();
    await _loadSessions();
    _refreshController.reverse();
  }

  void _navigateToChat(FreLtianSessionModel session) {
    // 创建AI角色模型用于导航
    final aiRole = FreAiRoleModel(
      roleId: session.roleId,
      roleName: session.roleName,
      avatarPath: session.roleAvatar,
      shortDesc: '',
      longDesc: '',
      tags: [],
      primaryColorHex: '#00E676',
      secondaryColorHex: '#00C853',
    );

    Navigator.push(
      context,
      CupertinoPageRoute(
        builder: (context) => FreLtianDialogVie(aiRole: aiRole),
      ),
    );
  }

  void _deleteSession(FreLtianSessionModel session) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Conversation'),
        content: const Text('Are you sure you want to delete this conversation? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Delete'),
            onPressed: () async {
              Navigator.pop(context);
              await _ltianService.deleteSession(session.sessionId);
              _refreshSessions();
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildSliverAppBar(),
          _buildStatsSection(),
          _buildSessionsList(),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: const Color(0xFFF8F9FA),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(
          CupertinoIcons.back,
          color: Color(0xFF333333),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: const Text(
        'Chat History',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Color(0xFF333333),
        ),
      ),
      actions: [
        AnimatedBuilder(
          animation: _refreshAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _refreshAnimation.value * 2 * 3.14159,
              child: IconButton(
                icon: const Icon(
                  CupertinoIcons.refresh,
                  color: Color(0xFF666666),
                ),
                onPressed: _refreshSessions,
              ),
            );
          },
        ),
        IconButton(
          icon: const Icon(
            CupertinoIcons.ellipsis_vertical,
            color: Color(0xFF666666),
          ),
          onPressed: () {
            _showMoreOptions();
          },
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          color: const Color(0xFFF8F9FA),
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    final stats = _ltianService.getSessionStats();
    
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: FreNeumorphicContainer(
          width: double.infinity,
          height: 100,
          backgroundColor: Colors.white,
          borderRadius: BorderRadius.circular(20),
          intensity: 0.12,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Conversations',
                    '${stats['totalSessions']}',
                    CupertinoIcons.chat_bubble_2_fill,
                    const Color(0xFF00E676),
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.grey.withOpacity(0.2),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Messages',
                    '${stats['totalMessages']}',
                    CupertinoIcons.text_bubble_fill,
                    const Color(0xFF3498DB),
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.grey.withOpacity(0.2),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Free Left',
                    '${stats['freeCount']}',
                    CupertinoIcons.gift_fill,
                    const Color(0xFFF39C12),
                  ),
                ),
              ],
            ),
          ),
        ),
      ).animate()
        .fadeIn(delay: 100.ms, duration: 600.ms)
        .slideY(begin: 0.3, end: 0, duration: 600.ms),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF888888),
          ),
        ),
      ],
    );
  }

  Widget _buildSessionsList() {
    if (_isLoading) {
      return const SliverFillRemaining(
        child: Center(
          child: CircularProgressIndicator(
            color: Color(0xFF00E676),
          ),
        ),
      );
    }

    if (_sessions.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.chat_bubble_2,
                size: 80,
                color: Colors.grey.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              const Text(
                'No conversations yet',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF888888),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Start chatting with an AI to see your history here',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF888888),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final session = _sessions[index];
            return _buildSessionCard(session, index);
          },
          childCount: _sessions.length,
        ),
      ),
    );
  }

  Widget _buildSessionCard(FreLtianSessionModel session, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: FreNeumorphicContainer(
        width: double.infinity,
        height: 100,
        backgroundColor: Colors.white,
        borderRadius: BorderRadius.circular(20),
        intensity: 0.12,
        onTap: () => _navigateToChat(session),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // AI角色头像
              Hero(
                tag: 'session_avatar_${session.sessionId}',
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF00E676).withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 3),
                      ),
                    ],
                    border: Border.all(
                      color: const Color(0xFF00E676),
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: Image.asset(
                      session.roleAvatar,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: const Color(0xFF00E676).withOpacity(0.2),
                          child: const Icon(
                            CupertinoIcons.person_fill,
                            size: 30,
                            color: Color(0xFF00E676),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // 会话信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      session.roleName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF333333),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      session.title.isNotEmpty
                          ? session.title
                          : 'New conversation',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          CupertinoIcons.time,
                          size: 12,
                          color: Colors.grey.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          session.formattedLastTime,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.withOpacity(0.8),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          CupertinoIcons.chat_bubble,
                          size: 12,
                          color: Colors.grey.withOpacity(0.6),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${session.messageCount}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 更多选项按钮
              GestureDetector(
                onTap: () => _showSessionOptions(session),
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Icon(
                    CupertinoIcons.ellipsis_vertical,
                    size: 16,
                    color: Color(0xFF888888),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate()
      .fadeIn(delay: (index * 100).ms, duration: 600.ms)
      .slideX(begin: 0.3, end: 0, duration: 600.ms);
  }

  void _showSessionOptions(FreLtianSessionModel session) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(session.roleName),
        message: Text(session.title.isNotEmpty ? session.title : 'Conversation options'),
        actions: [
          CupertinoActionSheetAction(
            child: const Text('Open Chat'),
            onPressed: () {
              Navigator.pop(context);
              _navigateToChat(session);
            },
          ),
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            child: const Text('Delete'),
            onPressed: () {
              Navigator.pop(context);
              _deleteSession(session);
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: const Text('Cancel'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _showMoreOptions() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('Chat History'),
        message: const Text('Manage your conversation history'),
        actions: [
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            child: const Text('Clear All'),
            onPressed: () {
              Navigator.pop(context);
              _showClearAllDialog();
            },
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: const Text('Cancel'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _showClearAllDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Clear All Conversations'),
        content: const Text('Are you sure you want to delete all conversations? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Clear All'),
            onPressed: () async {
              Navigator.pop(context);
              await _ltianService.clearAllSessions();
              _refreshSessions();
            },
          ),
        ],
      ),
    );
  }
}
