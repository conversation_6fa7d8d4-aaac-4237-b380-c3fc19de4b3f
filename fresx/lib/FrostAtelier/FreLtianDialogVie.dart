import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../AromaFlow/FreLtianStore.dart';
import '../FreshCore/FreAiRoleModel.dart';
import '../FreshCore/FreChatMsgModel.dart';
import '../IceChips/FreNeumorphicContainer.dart';
import '../IceChips/FreVipService.dart';

/// 聊天对话页面
class FreLtianDialogVie extends StatefulWidget {
  final FreAiRoleModel aiRole;

  const FreLtianDialogVie({
    Key? key,
    required this.aiRole,
  }) : super(key: key);

  @override
  State<FreLtianDialogVie> createState() => _FreLtianDialogVieState();
}

class _FreLtianDialogVieState extends State<FreLtianDialogVie>
    with TickerProviderStateMixin {
  late FreLtianStore _ltianStore;
  late TextEditingController _textController;
  late ScrollController _scrollController;
  late AnimationController _sendButtonController;
  late Animation<double> _sendButtonAnimation;

  @override
  void initState() {
    super.initState();
    _ltianStore = FreLtianStore();
    _textController = TextEditingController();
    _scrollController = ScrollController();
    
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _sendButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _sendButtonController,
      curve: Curves.easeInOut,
    ));

    _initializeChat();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _sendButtonController.dispose();
    super.dispose();
  }

  Future<void> _initializeChat() async {
    await _ltianStore.initializeSession(widget.aiRole);
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _handleSendMessage() async {
    if (_textController.text.trim().isEmpty) return;

    final messageText = _textController.text.trim();
    _ltianStore.updateInputText(messageText);

    _sendButtonController.forward().then((_) {
      _sendButtonController.reverse();
    });

    final result = await _ltianStore.sendMessage();

    // 只有在成功发送时才清空输入框
    if (result && _ltianStore.errorMessage == null) {
      _textController.clear();
    }

    _scrollToBottom();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: Observer(
        builder: (context) {
          if (_ltianStore.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                color: Color(0xFF00E676),
              ),
            );
          }

          return Column(
            children: [
              _buildFreeCountIndicator(),
              Expanded(
                child: _buildMessagesList(),
              ),
              _buildInputArea(),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFFF8F9FA),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(
          CupertinoIcons.back,
          color: Color(0xFF333333),
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Hero(
            tag: 'avatar_${widget.aiRole.roleId}',
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: widget.aiRole.primaryColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: widget.aiRole.primaryColor,
                  width: 2,
                ),
              ),
              child: ClipOval(
                child: Image.asset(
                  widget.aiRole.avatarPath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: widget.aiRole.primaryColor.withOpacity(0.2),
                      child: Icon(
                        CupertinoIcons.person_fill,
                        size: 20,
                        color: widget.aiRole.primaryColor,
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.aiRole.roleName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
                const Text(
                  'Online',
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF00E676),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(
            CupertinoIcons.ellipsis_vertical,
            color: Color(0xFF666666),
          ),
          onPressed: () {
            // 显示更多选项
          },
        ),
      ],
    );
  }

  Widget _buildFreeCountIndicator() {
    return Observer(
      builder: (context) {
        if (_ltianStore.freeCount <= 0) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            color: const Color(0xFFFFE5E5),
            child: Row(
              children: [
                const Icon(
                  CupertinoIcons.exclamationmark_triangle_fill,
                  color: Color(0xFFE74C3C),
                  size: 16,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Free messages used up. Upgrade to continue.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFFE74C3C),
                    ),
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // 跳转到升级页面
                  },
                  child: const Text(
                    'Upgrade',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFE74C3C),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: const Color(0xFFE8F5E8),
          child: Row(
            children: [
              const Icon(
                CupertinoIcons.chat_bubble_fill,
                color: Color(0xFF00E676),
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                '${_ltianStore.freeCount} free messages remaining',
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF00A651),
                ),
              ),
              const Spacer(),
              Container(
                width: 60,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _ltianStore.freeCountPercentage,
                  child: Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFF00E676),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMessagesList() {
    return Observer(
      builder: (context) {
        final messages = _ltianStore.messages;

        if (messages.isEmpty) {
          return const Center(
            child: Text(
              'Start a conversation...',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF888888),
              ),
            ),
          );
        }

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index];
            return _buildMessageBubble(message, index);
          },
        );
      },
    );
  }

  Widget _buildMessageBubble(FreChatMsgModel message, int index) {
    final isUser = message.isUser;
    final currentUser = FreVipService.instance.currentUser;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 8, top: 4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: widget.aiRole.primaryColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: Image.asset(
                  widget.aiRole.avatarPath,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: widget.aiRole.primaryColor.withOpacity(0.2),
                      child: Icon(
                        CupertinoIcons.person_fill,
                        size: 16,
                        color: widget.aiRole.primaryColor,
                      ),
                    );
                  },
                ),
              ),
            ),
          ],

          // 消息气泡
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              decoration: BoxDecoration(
                color: isUser
                    ? const Color(0xFF4A90E2)  // 优雅的蓝色
                    : Colors.white,
                borderRadius: BorderRadius.circular(18),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                  if (!isUser)
                    BoxShadow(
                      color: Colors.white.withOpacity(0.9),
                      blurRadius: 6,
                      offset: const Offset(-1, -1),
                    ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      message.content,
                      style: TextStyle(
                        fontSize: 16,
                        color: isUser ? Colors.white : const Color(0xFF333333),
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _formatTime(message.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: isUser
                            ? Colors.white.withOpacity(0.8)
                            : const Color(0xFF888888),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          if (isUser) ...[
            // 用户头像
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(left: 8, top: 4),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: currentUser?.levelColor ?? const Color(0xFF00E676),
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: currentUser != null
                    ? Image.asset(
                        currentUser.avatarPath,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: const Color(0xFF00E676).withOpacity(0.2),
                            child: const Icon(
                              CupertinoIcons.person_fill,
                              size: 16,
                              color: Color(0xFF00E676),
                            ),
                          );
                        },
                      )
                    : Container(
                        color: const Color(0xFF00E676).withOpacity(0.2),
                        child: const Icon(
                          CupertinoIcons.person_fill,
                          size: 16,
                          color: Color(0xFF00E676),
                        ),
                      ),
              ),
            ),
          ],
        ],
      ),
    ).animate()
      .fadeIn(delay: (index * 100).ms, duration: 400.ms)
      .slideY(begin: 0.3, end: 0, duration: 400.ms);
  }

  Widget _buildInputArea() {
    return Observer(
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: SafeArea(
            child: Row(
              children: [
                Expanded(
                  child: FreNeumorphicContainer(
                    width: double.infinity,
                    height: 48,
                    backgroundColor: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(24),
                    isInset: true,
                    intensity: 0.08,
                    child: TextField(
                      controller: _textController,
                      decoration: const InputDecoration(
                        hintText: 'Type a message...',
                        hintStyle: TextStyle(
                          color: Color(0xFF888888),
                          fontSize: 16,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(
                        color: Color(0xFF333333),
                        fontSize: 16,
                      ),
                      maxLines: null,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) => _handleSendMessage(),
                      onChanged: (text) {
                        _ltianStore.updateInputText(text);
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                AnimatedBuilder(
                  animation: _sendButtonAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _sendButtonAnimation.value,
                      child: FreNeumorphicContainer(
                        width: 48,
                        height: 48,
                        backgroundColor: _ltianStore.canSendMessage
                            ? const Color(0xFF00E676)
                            : const Color(0xFFE0E0E0),
                        borderRadius: BorderRadius.circular(24),
                        intensity: 0.12,
                        onTap: _ltianStore.canSendMessage ? _handleSendMessage : null,
                        child: _ltianStore.isSending
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Icon(
                                CupertinoIcons.paperplane_fill,
                                color: _ltianStore.canSendMessage
                                    ? Colors.white
                                    : const Color(0xFF888888),
                                size: 20,
                              ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inDays < 1) {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      return '${time.month}/${time.day}';
    }
  }
}
