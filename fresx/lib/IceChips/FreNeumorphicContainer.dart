import 'package:flutter/material.dart';

class FreNeumorphicContainer extends StatefulWidget {
  final Widget child;
  final double width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final bool isPressed;
  final bool isInset;
  final double intensity;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Duration animationDuration;

  const FreNeumorphicContainer({
    Key? key,
    required this.child,
    this.width = 200,
    this.height = 200,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.isPressed = false,
    this.isInset = false,
    this.intensity = 0.15,
    this.onTap,
    this.onLongPress,
    this.animationDuration = const Duration(milliseconds: 150),
  }) : super(key: key);

  @override
  State<FreNeumorphicContainer> createState() => _FreNeumorphicContainerState();
}

class _FreNeumorphicContainerState extends State<FreNeumorphicContainer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  void _handleTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.backgroundColor ?? const Color(0xFFF0F0F3);
    final isPressed = widget.isPressed || _isPressed;
    final isInset = widget.isInset;

    // 计算阴影颜色
    final lightShadow = backgroundColor.withOpacity(0.9);
    final darkShadow = Colors.black.withOpacity(widget.intensity);

    // 根据状态调整阴影
    List<BoxShadow> shadows;
    if (isInset) {
      // 内凹效果 - 使用反向阴影模拟
      shadows = [
        BoxShadow(
          color: lightShadow,
          offset: const Offset(4, 4),
          blurRadius: 8,
        ),
        BoxShadow(
          color: darkShadow,
          offset: const Offset(-4, -4),
          blurRadius: 8,
        ),
      ];
    } else if (isPressed) {
      // 按下状态 - 轻微内凹
      shadows = [
        BoxShadow(
          color: darkShadow,
          offset: const Offset(2, 2),
          blurRadius: 4,
        ),
        BoxShadow(
          color: lightShadow,
          offset: const Offset(-2, -2),
          blurRadius: 4,
        ),
      ];
    } else {
      // 正常外凸效果
      shadows = [
        BoxShadow(
          color: darkShadow,
          offset: const Offset(6, 6),
          blurRadius: 12,
        ),
        BoxShadow(
          color: lightShadow,
          offset: const Offset(-6, -6),
          blurRadius: 12,
        ),
      ];
    }

    Widget container = AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.onTap != null ? _scaleAnimation.value : 1.0,
          child: Container(
            width: widget.width,
            height: widget.height,
            padding: widget.padding,
            margin: widget.margin,
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: widget.borderRadius ?? BorderRadius.circular(20),
              boxShadow: shadows,
            ),
            child: widget.child,
          ),
        );
      },
    );

    if (widget.onTap != null || widget.onLongPress != null) {
      return GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onLongPress: widget.onLongPress,
        child: container,
      );
    }

    return container;
  }
}

// 新拟物化按钮组件
class FreNeumorphicButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final double width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const FreNeumorphicButton({
    Key? key,
    required this.child,
    this.onPressed,
    this.width = 120,
    this.height = 50,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FreNeumorphicContainer(
      width: width,
      height: height,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      backgroundColor: backgroundColor,
      borderRadius: borderRadius ?? BorderRadius.circular(25),
      onTap: onPressed,
      child: Center(child: child),
    );
  }
}

// 新拟物化卡片组件
class FreNeumorphicCard extends StatelessWidget {
  final Widget child;
  final double width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;

  const FreNeumorphicCard({
    Key? key,
    required this.child,
    this.width = double.infinity,
    this.height = 200,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FreNeumorphicContainer(
      width: width,
      height: height,
      padding: padding ?? const EdgeInsets.all(20),
      margin: margin ?? const EdgeInsets.all(8),
      backgroundColor: backgroundColor,
      borderRadius: borderRadius ?? BorderRadius.circular(20),
      onTap: onTap,
      child: child,
    );
  }
}
