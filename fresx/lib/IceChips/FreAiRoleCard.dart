import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../FreshCore/FreAiRoleModel.dart';
import 'FreNeumorphicContainer.dart';

class FreAiRoleCard extends StatefulWidget {
  final FreAiRoleModel aiRole;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final double cardWidth;
  final double cardHeight;

  const FreAiRoleCard({
    Key? key,
    required this.aiRole,
    this.onTap,
    this.onFavorite,
    this.cardWidth = 320,
    this.cardHeight = 420,
  }) : super(key: key);

  @override
  State<FreAiRoleCard> createState() => _FreAiRoleCardState();
}

class _FreAiRoleCardState extends State<FreAiRoleCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    final aiRole = widget.aiRole;
    
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: Container(
        width: widget.cardWidth,
        height: widget.cardHeight,
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFF0F0F3),
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            // 右下深色阴影
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              offset: const Offset(8, 8),
              blurRadius: 16,
              spreadRadius: 0,
            ),
            // 左上浅色阴影
            BoxShadow(
              color: Colors.white.withOpacity(0.9),
              offset: const Offset(-8, -8),
              blurRadius: 16,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(32),
            onTap: widget.onTap,
            child: Padding(
              padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // AI角色头像区域
            _buildAvatarSection(),
            
            const SizedBox(height: 20),
            
            // 角色名称
            _buildRoleName(),
            
            const SizedBox(height: 12),
            
            // 标签区域
            _buildTagsSection(),
            
            const SizedBox(height: 16),
            
            // 描述区域
            _buildDescriptionSection(),
            
            const Spacer(),
            
            // 底部操作区域
            _buildBottomSection(),
          ],
        ),
      ),
    ).animate()
      .fadeIn(duration: 600.ms, delay: 100.ms)
      .slideY(begin: 0.3, end: 0, duration: 600.ms, curve: Curves.easeOutCubic))));
  }

  Widget _buildAvatarSection() {
    return Stack(
      children: [
        // 主头像容器
        FreNeumorphicContainer(
          width: 120,
          height: 120,
          borderRadius: BorderRadius.circular(60),
          backgroundColor: const Color(0xFFF0F0F3),
          isInset: true,
          intensity: 0.1,
          child: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(52),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  widget.aiRole.primaryColor.withOpacity(0.3),
                  widget.aiRole.secondaryColor.withOpacity(0.3),
                ],
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(52),
              child: Image.asset(
                widget.aiRole.avatarPath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(52),
                      color: widget.aiRole.primaryColor.withOpacity(0.2),
                    ),
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: widget.aiRole.primaryColor,
                    ),
                  );
                },
              ),
            ),
          ),
        ),
        
        // 收藏按钮
        Positioned(
          top: 0,
          right: 0,
          child: FreNeumorphicContainer(
            width: 36,
            height: 36,
            borderRadius: BorderRadius.circular(18),
            backgroundColor: const Color(0xFFF0F0F3),
            onTap: widget.onFavorite,
            child: Icon(
              widget.aiRole.isFavorite ? Icons.favorite : Icons.favorite_border,
              size: 18,
              color: widget.aiRole.isFavorite 
                ? const Color(0xFFE74C3C) 
                : const Color(0xFF888888),
            ),
          ),
        ),
        
        // 免费次数指示器
        if (widget.aiRole.hasFreeChats)
          Positioned(
            bottom: 0,
            left: 0,
            child: FreNeumorphicContainer(
              width: 32,
              height: 32,
              borderRadius: BorderRadius.circular(16),
              backgroundColor: const Color(0xFF2ECC71),
              child: Center(
                child: Text(
                  '${widget.aiRole.freeChats}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRoleName() {
    return Text(
      widget.aiRole.roleName,
      style: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF333333),
        letterSpacing: 0.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildTagsSection() {
    return Wrap(
      spacing: 8,
      runSpacing: 6,
      alignment: WrapAlignment.center,
      children: widget.aiRole.tags.take(3).map((tag) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                widget.aiRole.primaryColor.withOpacity(0.8),
                widget.aiRole.primaryColor.withOpacity(0.6),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(14),
            boxShadow: [
              BoxShadow(
                color: widget.aiRole.primaryColor.withOpacity(0.3),
                offset: const Offset(0, 2),
                blurRadius: 4,
              ),
            ],
          ),
          child: Text(
            tag,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDescriptionSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Text(
        widget.aiRole.shortDesc,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
          height: 1.4,
        ),
        textAlign: TextAlign.center,
        maxLines: 3,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildBottomSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 聊天次数信息
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Free Chats',
              style: TextStyle(
                fontSize: 12,
                color: const Color(0xFF888888),
              ),
            ),
            const SizedBox(height: 2),
            Text(
              '${widget.aiRole.freeChats} left',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: widget.aiRole.hasFreeChats 
                  ? const Color(0xFF2ECC71) 
                  : const Color(0xFFE74C3C),
              ),
            ),
          ],
        ),
        
        // 开始聊天按钮
        Container(
          width: 100,
          height: 36,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [
                Color(0xFF4CAF50),
                Color(0xFF45A049),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              // 外发光效果
              BoxShadow(
                color: const Color(0xFF4CAF50).withOpacity(0.4),
                offset: const Offset(0, 0),
                blurRadius: 8,
                spreadRadius: 2,
              ),
              // 底部阴影
              BoxShadow(
                color: const Color(0xFF4CAF50).withOpacity(0.3),
                offset: const Offset(0, 4),
                blurRadius: 8,
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(18),
              onTap: widget.onTap,
              child: const Center(
                child: Text(
                  'Chat Now',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );

  }
}
