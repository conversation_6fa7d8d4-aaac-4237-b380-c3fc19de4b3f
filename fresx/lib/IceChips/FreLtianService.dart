import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../FreshCore/FreLtianSessionModel.dart';
import '../FreshCore/FreChatMsgModel.dart';
import '../FreshCore/FreAiRoleModel.dart';

/// 聊天服务
class FreLtianService {
  static const String _sessionsKey = 'fre_ltian_sessions';
  static const String _freeCountKey = 'fre_free_ltian_count';
  static const String _apiUrl = 'https://api.moonshot.cn/v1/chat/completions';
  static const String _apiKey = 'sk-UjLDXgyVsdh4kNtrLbbu1yFFhuafggJTxEw03ezpnnvXX2fR';
  static const int _maxFreeCount = 10; // 免费次数限制

  static FreLtianService? _instance;
  static List<FreLtianSessionModel> _sessions = [];
  static int _freeCount = _maxFreeCount;

  FreLtianService._();

  /// 获取单例实例
  static FreLtianService get instance {
    _instance ??= FreLtianService._();
    return _instance!;
  }

  /// 获取所有会话
  List<FreLtianSessionModel> get sessions => _sessions;

  /// 获取剩余免费次数
  int get freeCount => _freeCount;

  /// 获取最大免费次数
  int get maxFreeCount => _maxFreeCount;

  /// 是否还有免费次数
  bool get hasFreeCount => _freeCount > 0;

  /// 初始化服务
  Future<void> initialize() async {
    await _loadSessions();
    await _loadFreeCount();
  }

  /// 加载会话数据
  Future<void> _loadSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionsData = prefs.getString(_sessionsKey);
      
      if (sessionsData != null) {
        final List<dynamic> jsonList = jsonDecode(sessionsData);
        _sessions = jsonList
            .map((json) => FreLtianSessionModel.fromJson(json))
            .toList();
        
        // 按最后活动时间排序
        _sessions.sort((a, b) => b.lastTime.compareTo(a.lastTime));
      }
    } catch (e) {
      print('加载会话数据失败: $e');
      _sessions = [];
    }
  }

  /// 保存会话数据
  Future<void> _saveSessions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _sessions.map((session) => session.toJson()).toList();
      await prefs.setString(_sessionsKey, jsonEncode(jsonList));
    } catch (e) {
      print('保存会话数据失败: $e');
    }
  }

  /// 加载免费次数
  Future<void> _loadFreeCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _freeCount = prefs.getInt(_freeCountKey) ?? _maxFreeCount;
    } catch (e) {
      print('加载免费次数失败: $e');
      _freeCount = _maxFreeCount;
    }
  }

  /// 保存免费次数
  Future<void> _saveFreeCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_freeCountKey, _freeCount);
    } catch (e) {
      print('保存免费次数失败: $e');
    }
  }

  /// 获取或创建会话
  Future<FreLtianSessionModel> getOrCreateSession(FreAiRoleModel role) async {
    // 查找现有会话
    final existingSession = _sessions.firstWhere(
      (session) => session.roleId == role.roleId,
      orElse: () => FreLtianSessionModel.createFromRole(role),
    );

    // 如果是新会话，添加到列表
    if (!_sessions.contains(existingSession)) {
      _sessions.insert(0, existingSession);
      await _saveSessions();
    }

    return existingSession;
  }

  /// 发送消息
  Future<FreChatMsgModel?> sendMessage(
    FreLtianSessionModel session,
    String content,
  ) async {
    try {
      // 创建用户消息
      final userMessage = FreChatMsgModel.createUserMessage(
        roleId: session.roleId,
        content: content,
      );

      // 添加到会话
      session.addMessage(userMessage);
      await _saveSessions();

      // 构建消息历史
      final messages = [
        {
          'role': 'system',
          'content': 'You are ${session.roleName}, a helpful AI assistant. Please respond in a friendly and helpful manner.',
        },
        ...session.getMessageHistory(limit: 10),
      ];

      print('发送API请求: $messages');

      // 调用API
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_apiKey',
        },
        body: jsonEncode({
          'model': 'moonshot-v1-8k',
          'messages': messages,
          'temperature': 0.3,
        }),
      ).timeout(const Duration(seconds: 30));

      print('API响应状态码: ${response.statusCode}');
      print('API响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final aiContent = data['choices'][0]['message']['content'];

        // 创建AI回复消息
        final aiMessage = FreChatMsgModel.createAiMessage(
          roleId: session.roleId,
          content: aiContent,
        );

        // 添加到会话
        session.addMessage(aiMessage);
        await _saveSessions();

        // 扣除免费次数（只有在成功获取回复后才扣除）
        await _consumeFreeCount();

        return aiMessage;
      } else {
        print('API调用失败: ${response.statusCode}, 响应: ${response.body}');
        throw Exception('API调用失败: ${response.statusCode}');
      }
    } catch (e) {
      print('发送消息失败: $e');

      // 创建错误消息
      final errorMessage = FreChatMsgModel.createAiMessage(
        roleId: session.roleId,
        content: 'Sorry, I encountered an error. Please try again later.',
      );
      errorMessage.setStatus(FreMsgStatus.failed);

      session.addMessage(errorMessage);
      await _saveSessions();

      return errorMessage;
    }
  }

  /// 消费免费次数
  Future<void> _consumeFreeCount() async {
    if (_freeCount > 0) {
      _freeCount--;
      await _saveFreeCount();
    }
  }

  /// 删除会话
  Future<void> deleteSession(String sessionId) async {
    _sessions.removeWhere((session) => session.sessionId == sessionId);
    await _saveSessions();
  }

  /// 清空所有会话
  Future<void> clearAllSessions() async {
    _sessions.clear();
    await _saveSessions();
  }

  /// 重置免费次数（用于测试）
  Future<void> resetFreeCount() async {
    _freeCount = _maxFreeCount;
    await _saveFreeCount();
  }

  /// 获取会话统计
  Map<String, dynamic> getSessionStats() {
    return {
      'totalSessions': _sessions.length,
      'totalMessages': _sessions.fold(0, (sum, session) => sum + session.messageCount),
      'freeCount': _freeCount,
      'maxFreeCount': _maxFreeCount,
    };
  }
}
