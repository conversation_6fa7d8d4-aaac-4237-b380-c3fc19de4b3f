import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'FreVipModel.dart';

/// Fresx用户数据管理服务
class FreVipService {
  static const String _userKey = 'fre_vip_data';
  static FreVipService? _instance;
  static FreVipModel? _currentUser;

  FreVipService._();

  /// 获取单例实例
  static FreVipService get instance {
    _instance ??= FreVipService._();
    return _instance!;
  }

  /// 获取当前用户
  FreVipModel? get currentUser => _currentUser;

  /// 更新免费聊天次数
  void updateFreeChats(int count) {
    if (_currentUser != null) {
      _currentUser!.freeChats = count;
      saveUserData(_currentUser!);
    }
  }

  /// 初始化用户数据
  Future<void> initialize() async {
    await _loadUserData();
  }

  /// 加载用户数据
  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString(_userKey);
      
      if (userData != null) {
        final json = jsonDecode(userData);
        _currentUser = FreVipModel.fromJson(json);
      } else {
        // 首次使用，创建默认用户
        _currentUser = FreVipModel.defaultUser;
        await _saveUserData();
      }
    } catch (e) {
      print('加载用户数据失败: $e');
      _currentUser = FreVipModel.defaultUser;
    }
  }

  /// 保存用户数据
  Future<void> _saveUserData() async {
    if (_currentUser == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final userData = jsonEncode(_currentUser!.toJson());
      await prefs.setString(_userKey, userData);
    } catch (e) {
      print('保存用户数据失败: $e');
    }
  }

  /// 更新用户昵称
  Future<bool> updateNickname(String nickname) async {
    if (_currentUser == null || nickname.trim().isEmpty) return false;
    
    try {
      _currentUser = _currentUser!.copyWith(nickname: nickname.trim());
      await _saveUserData();
      return true;
    } catch (e) {
      print('更新昵称失败: $e');
      return false;
    }
  }

  /// 更新用户头像
  Future<bool> updateAvatar(String avatarPath) async {
    if (_currentUser == null || avatarPath.trim().isEmpty) return false;
    
    try {
      _currentUser = _currentUser!.copyWith(avatarPath: avatarPath.trim());
      await _saveUserData();
      return true;
    } catch (e) {
      print('更新头像失败: $e');
      return false;
    }
  }

  /// 更新用户简介
  Future<bool> updateBio(String bio) async {
    if (_currentUser == null) return false;
    
    try {
      _currentUser = _currentUser!.copyWith(bio: bio.trim());
      await _saveUserData();
      return true;
    } catch (e) {
      print('更新简介失败: $e');
      return false;
    }
  }

  /// 增加用户经验值
  Future<bool> addExperience(int exp) async {
    if (_currentUser == null || exp <= 0) return false;
    
    try {
      final newExp = _currentUser!.experience + exp;
      final newLevel = (newExp ~/ 1000) + 1;
      
      _currentUser = _currentUser!.copyWith(
        experience: newExp,
        level: newLevel > 5 ? 5 : newLevel,
      );
      
      await _saveUserData();
      return true;
    } catch (e) {
      print('增加经验值失败: $e');
      return false;
    }
  }

  /// 添加成就
  Future<bool> addAchievement(String achievement) async {
    if (_currentUser == null || achievement.trim().isEmpty) return false;
    
    try {
      final achievements = List<String>.from(_currentUser!.achievements);
      if (!achievements.contains(achievement)) {
        achievements.add(achievement);
        _currentUser = _currentUser!.copyWith(achievements: achievements);
        await _saveUserData();
      }
      return true;
    } catch (e) {
      print('添加成就失败: $e');
      return false;
    }
  }

  /// 更新用户偏好设置
  Future<bool> updatePreference(String key, dynamic value) async {
    if (_currentUser == null || key.trim().isEmpty) return false;
    
    try {
      final preferences = Map<String, dynamic>.from(_currentUser!.preferences);
      preferences[key] = value;
      _currentUser = _currentUser!.copyWith(preferences: preferences);
      await _saveUserData();
      return true;
    } catch (e) {
      print('更新偏好设置失败: $e');
      return false;
    }
  }

  /// 重置用户数据
  Future<bool> resetUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      _currentUser = FreVipModel.defaultUser;
      await _saveUserData();
      return true;
    } catch (e) {
      print('重置用户数据失败: $e');
      return false;
    }
  }

  /// 获取用户统计信息
  Map<String, dynamic> getUserStats() {
    if (_currentUser == null) return {};
    
    return {
      'joinDays': DateTime.now().difference(_currentUser!.joinDate).inDays,
      'level': _currentUser!.level,
      'experience': _currentUser!.experience,
      'achievements': _currentUser!.achievements.length,
      'levelProgress': _currentUser!.levelProgress,
      'nextLevelExp': _currentUser!.nextLevelExp,
    };
  }
}
