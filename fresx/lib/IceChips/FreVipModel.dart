import 'package:flutter/material.dart';

/// Fresx用户模型
class FreVipModel {
  final String vipId;
  final String nickname;
  final String avatarPath;
  final String email;
  final DateTime joinDate;
  final int level;
  final int experience;
  final String bio;
  final List<String> achievements;
  final Map<String, dynamic> preferences;

  const FreVipModel({
    required this.vipId,
    required this.nickname,
    required this.avatarPath,
    required this.email,
    required this.joinDate,
    this.level = 1,
    this.experience = 0,
    this.bio = '',
    this.achievements = const [],
    this.preferences = const {},
  });

  /// 从JSON创建用户模型
  factory FreVipModel.fromJson(Map<String, dynamic> json) {
    return FreVipModel(
      vipId: json['vipId'] ?? '',
      nickname: json['nickname'] ?? '',
      avatarPath: json['avatarPath'] ?? '',
      email: json['email'] ?? '',
      joinDate: DateTime.parse(json['joinDate'] ?? DateTime.now().toIso8601String()),
      level: json['level'] ?? 1,
      experience: json['experience'] ?? 0,
      bio: json['bio'] ?? '',
      achievements: List<String>.from(json['achievements'] ?? []),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'vipId': vipId,
      'nickname': nickname,
      'avatarPath': avatarPath,
      'email': email,
      'joinDate': joinDate.toIso8601String(),
      'level': level,
      'experience': experience,
      'bio': bio,
      'achievements': achievements,
      'preferences': preferences,
    };
  }

  /// 复制并修改用户信息
  FreVipModel copyWith({
    String? vipId,
    String? nickname,
    String? avatarPath,
    String? email,
    DateTime? joinDate,
    int? level,
    int? experience,
    String? bio,
    List<String>? achievements,
    Map<String, dynamic>? preferences,
  }) {
    return FreVipModel(
      vipId: vipId ?? this.vipId,
      nickname: nickname ?? this.nickname,
      avatarPath: avatarPath ?? this.avatarPath,
      email: email ?? this.email,
      joinDate: joinDate ?? this.joinDate,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      bio: bio ?? this.bio,
      achievements: achievements ?? this.achievements,
      preferences: preferences ?? this.preferences,
    );
  }

  /// 获取等级颜色
  Color get levelColor {
    switch (level) {
      case 1:
        return const Color(0xFF95A5A6); // 银色
      case 2:
        return const Color(0xFFF39C12); // 金色
      case 3:
        return const Color(0xFF9B59B6); // 紫色
      case 4:
        return const Color(0xFF3498DB); // 蓝色
      case 5:
        return const Color(0xFFE74C3C); // 红色
      default:
        return const Color(0xFF2ECC71); // 绿色
    }
  }

  /// 获取等级名称
  String get levelName {
    switch (level) {
      case 1:
        return 'Frost Novice';
      case 2:
        return 'Ice Explorer';
      case 3:
        return 'Crystal Master';
      case 4:
        return 'Frost Legend';
      case 5:
        return 'Ice Emperor';
      default:
        return 'Frost Creator';
    }
  }

  /// 获取下一级所需经验
  int get nextLevelExp {
    return level * 1000;
  }

  /// 获取当前等级进度
  double get levelProgress {
    if (level >= 5) return 1.0;
    return (experience % 1000) / 1000.0;
  }

  /// 默认用户数据
  static FreVipModel get defaultUser {
    return FreVipModel(
      vipId: '${DateTime.now().millisecondsSinceEpoch}',
      nickname: 'Alex',
      avatarPath: 'assets/images/avatars/fre_avatar_01.jpg',
      email: '<EMAIL>',
      joinDate: DateTime.now(),
      level: 1,
      experience: 0,
      bio: '',
      achievements: ['First Login'],
      preferences: {
        'theme': 'light',
        'notifications': true,
        'language': 'en',
      },
    );
  }

  /// 预设昵称列表
  static List<String> get presetNicknames {
    return [
      'Alex',
      'Jordan',
      'Taylor',
      'Casey',
      'Morgan',
      'Riley',
      'Avery',
      'Quinn',
      'Blake',
      'Cameron',
    ];
  }

  /// 预设头像路径列表
  static List<String> get presetAvatars {
    return [
      'assets/images/avatars/fre_avatar_01.jpg',
      'assets/images/avatars/fre_avatar_02.jpg',
      'assets/images/avatars/fre_avatar_03.jpg',
      'assets/images/avatars/fre_avatar_04.jpg',
      'assets/images/avatars/fre_avatar_05.jpg',
      'assets/images/avatars/fre_avatar_06.jpg',
    ];
  }
}
