// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreLtianService.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$FreLtianService on _FreLtianService, Store {
  late final _$_sessionsAtom =
      Atom(name: '_FreLtianService._sessions', context: context);

  @override
  ObservableList<FreLtianSessionModel> get _sessions {
    _$_sessionsAtom.reportRead();
    return super._sessions;
  }

  @override
  set _sessions(ObservableList<FreLtianSessionModel> value) {
    _$_sessionsAtom.reportWrite(value, super._sessions, () {
      super._sessions = value;
    });
  }

  late final _$_freeCountAtom =
      Atom(name: '_FreLtianService._freeCount', context: context);

  @override
  int get _freeCount {
    _$_freeCountAtom.reportRead();
    return super._freeCount;
  }

  @override
  set _freeCount(int value) {
    _$_freeCountAtom.reportWrite(value, super._freeCount, () {
      super._freeCount = value;
    });
  }

  late final _$_consumeFreeCountAsyncAction =
      AsyncAction('_FreLtianService._consumeFreeCount', context: context);

  @override
  Future<void> _consumeFreeCount() {
    return _$_consumeFreeCountAsyncAction.run(() => super._consumeFreeCount());
  }

  @override
  String toString() {
    return '''

    ''';
  }
}
