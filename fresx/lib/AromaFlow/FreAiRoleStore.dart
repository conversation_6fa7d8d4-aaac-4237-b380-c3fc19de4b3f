import 'package:mobx/mobx.dart';
import '../FreshCore/FreAiRoleModel.dart';

part 'FreAiRoleStore.g.dart';

class FreAiRoleStore = _FreAiRoleStoreBase with _$FreAiRoleStore;

abstract class _FreAiRoleStoreBase with Store {
  @observable
  ObservableList<FreAiRoleModel> aiRoles = ObservableList<FreAiRoleModel>();

  @observable
  FreAiRoleModel? selectedRole;

  @observable
  String searchQuery = '';

  @observable
  String selectedTag = '';

  @observable
  bool isLoading = false;

  @observable
  String? errorMessage;

  // 获取所有可用的标签
  @computed
  List<String> get availableTags {
    final allTags = <String>{};
    for (final role in aiRoles) {
      allTags.addAll(role.tags);
    }
    return allTags.toList()..sort();
  }

  // 获取过滤后的AI角色列表
  @computed
  List<FreAiRoleModel> get filteredRoles {
    var filtered = aiRoles.toList();

    // 按搜索关键词过滤
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((role) {
        return role.roleName.toLowerCase().contains(searchQuery.toLowerCase()) ||
               role.shortDesc.toLowerCase().contains(searchQuery.toLowerCase()) ||
               role.tags.any((tag) => tag.toLowerCase().contains(searchQuery.toLowerCase()));
      }).toList();
    }

    // 按标签过滤
    if (selectedTag.isNotEmpty) {
      filtered = filtered.where((role) {
        return role.tags.contains(selectedTag);
      }).toList();
    }

    return filtered;
  }

  // 获取收藏的角色
  @computed
  List<FreAiRoleModel> get favoriteRoles {
    return aiRoles.where((role) => role.isFavorite).toList();
  }

  // 获取有免费聊天次数的角色
  @computed
  List<FreAiRoleModel> get rolesWithFreeChats {
    return aiRoles.where((role) => role.hasFreeChats).toList();
  }

  // 初始化AI角色数据
  @action
  Future<void> initializeRoles() async {
    try {
      isLoading = true;
      errorMessage = null;

      // 加载默认AI角色数据
      final defaultRoles = FreAiRoleModel.createDefaultRoles();
      aiRoles.clear();
      aiRoles.addAll(defaultRoles);

      // 这里可以添加从本地数据库加载用户自定义数据的逻辑
      await _loadUserCustomizations();

    } catch (e) {
      errorMessage = 'Failed to load AI roles: $e';
    } finally {
      isLoading = false;
    }
  }

  // 加载用户自定义设置（收藏状态、聊天次数等）
  @action
  Future<void> _loadUserCustomizations() async {
    // TODO: 从SQLite数据库加载用户的收藏状态和聊天次数
    // 这里暂时使用模拟数据
    await Future.delayed(const Duration(milliseconds: 500));
  }

  // 选择AI角色
  @action
  void selectRole(FreAiRoleModel role) {
    selectedRole = role;
  }

  // 清除选择
  @action
  void clearSelection() {
    selectedRole = null;
  }

  // 切换角色收藏状态
  @action
  void toggleRoleFavorite(String roleId) {
    final roleIndex = aiRoles.indexWhere((role) => role.roleId == roleId);
    if (roleIndex != -1) {
      aiRoles[roleIndex].toggleFavorite();
      // TODO: 保存到数据库
    }
  }

  // 消耗角色的免费聊天次数
  @action
  bool consumeRoleFreeChat(String roleId) {
    final roleIndex = aiRoles.indexWhere((role) => role.roleId == roleId);
    if (roleIndex != -1 && aiRoles[roleIndex].hasFreeChats) {
      aiRoles[roleIndex].consumeFreeChat();
      // TODO: 保存到数据库
      return true;
    }
    return false;
  }

  // 重置角色免费聊天次数
  @action
  void resetRoleFreeChats(String roleId, {int count = 3}) {
    final roleIndex = aiRoles.indexWhere((role) => role.roleId == roleId);
    if (roleIndex != -1) {
      aiRoles[roleIndex].resetFreeChats(count: count);
      // TODO: 保存到数据库
    }
  }

  // 设置搜索关键词
  @action
  void setSearchQuery(String query) {
    searchQuery = query;
  }

  // 设置选中的标签
  @action
  void setSelectedTag(String tag) {
    selectedTag = tag;
  }

  // 清除搜索和过滤
  @action
  void clearFilters() {
    searchQuery = '';
    selectedTag = '';
  }

  // 根据ID获取角色
  FreAiRoleModel? getRoleById(String roleId) {
    try {
      return aiRoles.firstWhere((role) => role.roleId == roleId);
    } catch (e) {
      return null;
    }
  }

  // 刷新角色数据
  @action
  Future<void> refreshRoles() async {
    await initializeRoles();
  }

  // 保存角色数据到本地
  @action
  Future<void> saveRolesToLocal() async {
    // TODO: 实现保存到SQLite数据库的逻辑
    try {
      // 保存角色的收藏状态、聊天次数等用户相关数据
      for (final role in aiRoles) {
        // 保存到数据库...
      }
    } catch (e) {
      errorMessage = 'Failed to save roles: $e';
    }
  }

  // 获取角色统计信息
  @computed
  Map<String, int> get roleStats {
    return {
      'total': aiRoles.length,
      'favorites': favoriteRoles.length,
      'withFreeChats': rolesWithFreeChats.length,
      'totalChats': aiRoles.fold(0, (sum, role) => sum + role.totalChats),
    };
  }
}
