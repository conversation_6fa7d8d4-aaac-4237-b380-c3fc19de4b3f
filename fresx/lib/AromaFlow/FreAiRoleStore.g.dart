// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreAiRoleStore.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$FreAiRoleStore on _FreAiRoleStoreBase, Store {
  Computed<List<String>>? _$availableTagsComputed;

  @override
  List<String> get availableTags => (_$availableTagsComputed ??=
          Computed<List<String>>(() => super.availableTags,
              name: '_FreAiRoleStoreBase.availableTags'))
      .value;
  Computed<List<FreAiRoleModel>>? _$filteredRolesComputed;

  @override
  List<FreAiRoleModel> get filteredRoles => (_$filteredRolesComputed ??=
          Computed<List<FreAiRoleModel>>(() => super.filteredRoles,
              name: '_FreAiRoleStoreBase.filteredRoles'))
      .value;
  Computed<List<FreAiRoleModel>>? _$favoriteRolesComputed;

  @override
  List<FreAiRoleModel> get favoriteRoles => (_$favoriteRolesComputed ??=
          Computed<List<FreAiRoleModel>>(() => super.favoriteRoles,
              name: '_FreAiRoleStoreBase.favoriteRoles'))
      .value;
  Computed<List<FreAiRoleModel>>? _$rolesWithFreeChatsComputed;

  @override
  List<FreAiRoleModel> get rolesWithFreeChats =>
      (_$rolesWithFreeChatsComputed ??= Computed<List<FreAiRoleModel>>(
              () => super.rolesWithFreeChats,
              name: '_FreAiRoleStoreBase.rolesWithFreeChats'))
          .value;
  Computed<Map<String, int>>? _$roleStatsComputed;

  @override
  Map<String, int> get roleStats =>
      (_$roleStatsComputed ??= Computed<Map<String, int>>(() => super.roleStats,
              name: '_FreAiRoleStoreBase.roleStats'))
          .value;

  late final _$aiRolesAtom =
      Atom(name: '_FreAiRoleStoreBase.aiRoles', context: context);

  @override
  ObservableList<FreAiRoleModel> get aiRoles {
    _$aiRolesAtom.reportRead();
    return super.aiRoles;
  }

  @override
  set aiRoles(ObservableList<FreAiRoleModel> value) {
    _$aiRolesAtom.reportWrite(value, super.aiRoles, () {
      super.aiRoles = value;
    });
  }

  late final _$selectedRoleAtom =
      Atom(name: '_FreAiRoleStoreBase.selectedRole', context: context);

  @override
  FreAiRoleModel? get selectedRole {
    _$selectedRoleAtom.reportRead();
    return super.selectedRole;
  }

  @override
  set selectedRole(FreAiRoleModel? value) {
    _$selectedRoleAtom.reportWrite(value, super.selectedRole, () {
      super.selectedRole = value;
    });
  }

  late final _$searchQueryAtom =
      Atom(name: '_FreAiRoleStoreBase.searchQuery', context: context);

  @override
  String get searchQuery {
    _$searchQueryAtom.reportRead();
    return super.searchQuery;
  }

  @override
  set searchQuery(String value) {
    _$searchQueryAtom.reportWrite(value, super.searchQuery, () {
      super.searchQuery = value;
    });
  }

  late final _$selectedTagAtom =
      Atom(name: '_FreAiRoleStoreBase.selectedTag', context: context);

  @override
  String get selectedTag {
    _$selectedTagAtom.reportRead();
    return super.selectedTag;
  }

  @override
  set selectedTag(String value) {
    _$selectedTagAtom.reportWrite(value, super.selectedTag, () {
      super.selectedTag = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_FreAiRoleStoreBase.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$errorMessageAtom =
      Atom(name: '_FreAiRoleStoreBase.errorMessage', context: context);

  @override
  String? get errorMessage {
    _$errorMessageAtom.reportRead();
    return super.errorMessage;
  }

  @override
  set errorMessage(String? value) {
    _$errorMessageAtom.reportWrite(value, super.errorMessage, () {
      super.errorMessage = value;
    });
  }

  late final _$initializeRolesAsyncAction =
      AsyncAction('_FreAiRoleStoreBase.initializeRoles', context: context);

  @override
  Future<void> initializeRoles() {
    return _$initializeRolesAsyncAction.run(() => super.initializeRoles());
  }

  late final _$_loadUserCustomizationsAsyncAction = AsyncAction(
      '_FreAiRoleStoreBase._loadUserCustomizations',
      context: context);

  @override
  Future<void> _loadUserCustomizations() {
    return _$_loadUserCustomizationsAsyncAction
        .run(() => super._loadUserCustomizations());
  }

  late final _$refreshRolesAsyncAction =
      AsyncAction('_FreAiRoleStoreBase.refreshRoles', context: context);

  @override
  Future<void> refreshRoles() {
    return _$refreshRolesAsyncAction.run(() => super.refreshRoles());
  }

  late final _$saveRolesToLocalAsyncAction =
      AsyncAction('_FreAiRoleStoreBase.saveRolesToLocal', context: context);

  @override
  Future<void> saveRolesToLocal() {
    return _$saveRolesToLocalAsyncAction.run(() => super.saveRolesToLocal());
  }

  late final _$_FreAiRoleStoreBaseActionController =
      ActionController(name: '_FreAiRoleStoreBase', context: context);

  @override
  void selectRole(FreAiRoleModel role) {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.selectRole');
    try {
      return super.selectRole(role);
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearSelection() {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.clearSelection');
    try {
      return super.clearSelection();
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void toggleRoleFavorite(String roleId) {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.toggleRoleFavorite');
    try {
      return super.toggleRoleFavorite(roleId);
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  bool consumeRoleFreeChat(String roleId) {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.consumeRoleFreeChat');
    try {
      return super.consumeRoleFreeChat(roleId);
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void resetRoleFreeChats(String roleId, {int count = 3}) {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.resetRoleFreeChats');
    try {
      return super.resetRoleFreeChats(roleId, count: count);
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSearchQuery(String query) {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.setSearchQuery');
    try {
      return super.setSearchQuery(query);
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSelectedTag(String tag) {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.setSelectedTag');
    try {
      return super.setSelectedTag(tag);
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearFilters() {
    final _$actionInfo = _$_FreAiRoleStoreBaseActionController.startAction(
        name: '_FreAiRoleStoreBase.clearFilters');
    try {
      return super.clearFilters();
    } finally {
      _$_FreAiRoleStoreBaseActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
aiRoles: ${aiRoles},
selectedRole: ${selectedRole},
searchQuery: ${searchQuery},
selectedTag: ${selectedTag},
isLoading: ${isLoading},
errorMessage: ${errorMessage},
availableTags: ${availableTags},
filteredRoles: ${filteredRoles},
favoriteRoles: ${favoriteRoles},
rolesWithFreeChats: ${rolesWithFreeChats},
roleStats: ${roleStats}
    ''';
  }
}
