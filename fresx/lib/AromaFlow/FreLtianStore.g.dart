// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'FreLtianStore.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$FreLtianStore on _FreLtianStore, Store {
  Computed<int>? _$freeCountComputed;

  @override
  int get freeCount =>
      (_$freeCountComputed ??= Computed<int>(() => super.freeCount,
              name: '_FreLtianStore.freeCount'))
          .value;
  Computed<int>? _$maxFreeCountComputed;

  @override
  int get maxFreeCount =>
      (_$maxFreeCountComputed ??= Computed<int>(() => super.maxFreeCount,
              name: '_FreLtianStore.maxFreeCount'))
          .value;
  Computed<bool>? _$hasFreeCountComputed;

  @override
  bool get hasFreeCount =>
      (_$hasFreeCountComputed ??= Computed<bool>(() => super.hasFreeCount,
              name: '_FreLtianStore.hasFreeCount'))
          .value;
  Computed<double>? _$freeCountPercentageComputed;

  @override
  double get freeCountPercentage => (_$freeCountPercentageComputed ??=
          Computed<double>(() => super.freeCountPercentage,
              name: '_FreLtianStore.freeCountPercentage'))
      .value;
  Computed<List<FreChatMsgModel>>? _$messagesComputed;

  @override
  List<FreChatMsgModel> get messages => (_$messagesComputed ??=
          Computed<List<FreChatMsgModel>>(() => super.messages,
              name: '_FreLtianStore.messages'))
      .value;
  Computed<bool>? _$canSendMessageComputed;

  @override
  bool get canSendMessage =>
      (_$canSendMessageComputed ??= Computed<bool>(() => super.canSendMessage,
              name: '_FreLtianStore.canSendMessage'))
          .value;

  late final _$currentSessionAtom =
      Atom(name: '_FreLtianStore.currentSession', context: context);

  @override
  FreLtianSessionModel? get currentSession {
    _$currentSessionAtom.reportRead();
    return super.currentSession;
  }

  @override
  set currentSession(FreLtianSessionModel? value) {
    _$currentSessionAtom.reportWrite(value, super.currentSession, () {
      super.currentSession = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_FreLtianStore.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$isSendingAtom =
      Atom(name: '_FreLtianStore.isSending', context: context);

  @override
  bool get isSending {
    _$isSendingAtom.reportRead();
    return super.isSending;
  }

  @override
  set isSending(bool value) {
    _$isSendingAtom.reportWrite(value, super.isSending, () {
      super.isSending = value;
    });
  }

  late final _$inputTextAtom =
      Atom(name: '_FreLtianStore.inputText', context: context);

  @override
  String get inputText {
    _$inputTextAtom.reportRead();
    return super.inputText;
  }

  @override
  set inputText(String value) {
    _$inputTextAtom.reportWrite(value, super.inputText, () {
      super.inputText = value;
    });
  }

  late final _$errorMessageAtom =
      Atom(name: '_FreLtianStore.errorMessage', context: context);

  @override
  String? get errorMessage {
    _$errorMessageAtom.reportRead();
    return super.errorMessage;
  }

  @override
  set errorMessage(String? value) {
    _$errorMessageAtom.reportWrite(value, super.errorMessage, () {
      super.errorMessage = value;
    });
  }

  late final _$typingMessageAtom =
      Atom(name: '_FreLtianStore.typingMessage', context: context);

  @override
  FreChatMsgModel? get typingMessage {
    _$typingMessageAtom.reportRead();
    return super.typingMessage;
  }

  @override
  set typingMessage(FreChatMsgModel? value) {
    _$typingMessageAtom.reportWrite(value, super.typingMessage, () {
      super.typingMessage = value;
    });
  }

  late final _$initializeSessionAsyncAction =
      AsyncAction('_FreLtianStore.initializeSession', context: context);

  @override
  Future<void> initializeSession(FreAiRoleModel role) {
    return _$initializeSessionAsyncAction
        .run(() => super.initializeSession(role));
  }

  late final _$sendMessageAsyncAction =
      AsyncAction('_FreLtianStore.sendMessage', context: context);

  @override
  Future<void> sendMessage() {
    return _$sendMessageAsyncAction.run(() => super.sendMessage());
  }

  late final _$retryMessageAsyncAction =
      AsyncAction('_FreLtianStore.retryMessage', context: context);

  @override
  Future<void> retryMessage(FreChatMsgModel message) {
    return _$retryMessageAsyncAction.run(() => super.retryMessage(message));
  }

  late final _$_FreLtianStoreActionController =
      ActionController(name: '_FreLtianStore', context: context);

  @override
  void updateInputText(String text) {
    final _$actionInfo = _$_FreLtianStoreActionController.startAction(
        name: '_FreLtianStore.updateInputText');
    try {
      return super.updateInputText(text);
    } finally {
      _$_FreLtianStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearError() {
    final _$actionInfo = _$_FreLtianStoreActionController.startAction(
        name: '_FreLtianStore.clearError');
    try {
      return super.clearError();
    } finally {
      _$_FreLtianStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearCurrentSession() {
    final _$actionInfo = _$_FreLtianStoreActionController.startAction(
        name: '_FreLtianStore.clearCurrentSession');
    try {
      return super.clearCurrentSession();
    } finally {
      _$_FreLtianStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  void deleteMessage(String messageId) {
    final _$actionInfo = _$_FreLtianStoreActionController.startAction(
        name: '_FreLtianStore.deleteMessage');
    try {
      return super.deleteMessage(messageId);
    } finally {
      _$_FreLtianStoreActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
currentSession: ${currentSession},
isLoading: ${isLoading},
isSending: ${isSending},
inputText: ${inputText},
errorMessage: ${errorMessage},
typingMessage: ${typingMessage},
freeCount: ${freeCount},
maxFreeCount: ${maxFreeCount},
hasFreeCount: ${hasFreeCount},
freeCountPercentage: ${freeCountPercentage},
messages: ${messages},
canSendMessage: ${canSendMessage}
    ''';
  }
}
