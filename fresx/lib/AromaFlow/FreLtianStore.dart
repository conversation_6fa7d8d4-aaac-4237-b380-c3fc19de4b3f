import 'package:mobx/mobx.dart';
import '../FreshCore/FreLtianSessionModel.dart';
import '../FreshCore/FreChatMsgModel.dart';
import '../FreshCore/FreAiRoleModel.dart';
import '../IceChips/FreLtianService.dart';

part 'FreLtianStore.g.dart';

/// 聊天页面状态管理
class FreLtianStore = _FreLtianStore with _$FreLtianStore;

abstract class _FreLtianStore with Store {
  final FreLtianService _ltianService = FreLtianService.instance;

  @observable
  FreLtianSessionModel? currentSession;

  @observable
  bool isLoading = false;

  @observable
  bool isSending = false;

  @observable
  String inputText = '';

  @observable
  String? errorMessage;

  @observable
  FreChatMsgModel? typingMessage;

  /// 初始化聊天会话
  @action
  Future<void> initializeSession(FreAiRoleModel role) async {
    try {
      isLoading = true;
      errorMessage = null;
      
      currentSession = await _ltianService.getOrCreateSession(role);
      
      // 如果是新会话且没有消息，添加欢迎消息
      if (currentSession!.messages.isEmpty) {
        final welcomeMessage = currentSession!.createWelcomeMessage();
        currentSession!.addMessage(welcomeMessage);
      }
      
    } catch (e) {
      errorMessage = 'Failed to initialize chat: $e';
    } finally {
      isLoading = false;
    }
  }

  /// 发送消息
  @action
  Future<void> sendMessage() async {
    if (inputText.trim().isEmpty || isSending || currentSession == null) {
      return;
    }

    try {
      isSending = true;
      errorMessage = null;

      final messageContent = inputText.trim();

      // 检查免费次数，如果用完了显示提示
      if (!_ltianService.hasFreeCount) {
        errorMessage = 'You have used up all your free messages. Please upgrade to continue chatting.';
        // 不清空输入框，保留用户输入的内容
        return; // 直接返回，不发送消息
      }

      // 清空输入框（只有在有免费次数时才清空）
      inputText = '';

      // 发送消息并获取AI回复
      final aiMessage = await _ltianService.sendMessage(currentSession!, messageContent);

      if (aiMessage != null) {
        // 直接显示AI回复，不使用打字机效果
        aiMessage.completeTyping();
      } else {
        errorMessage = 'Failed to send message. Please try again.';
        // 如果发送失败，恢复输入框内容
        inputText = messageContent;
      }

    } catch (e) {
      errorMessage = 'Error sending message: $e';
    } finally {
      isSending = false;
      typingMessage = null;
    }
  }



  /// 更新输入文本
  @action
  void updateInputText(String text) {
    inputText = text;
  }

  /// 清除错误消息
  @action
  void clearError() {
    errorMessage = null;
  }

  /// 重试发送消息
  @action
  Future<void> retryMessage(FreChatMsgModel message) async {
    if (message.isFailed) {
      message.retry();
      // 这里可以添加重试逻辑
    }
  }

  /// 获取剩余免费次数
  @computed
  int get freeCount => _ltianService.freeCount;

  /// 获取最大免费次数
  @computed
  int get maxFreeCount => _ltianService.maxFreeCount;

  /// 是否还有免费次数
  @computed
  bool get hasFreeCount => _ltianService.hasFreeCount;

  /// 获取免费次数百分比
  @computed
  double get freeCountPercentage {
    if (maxFreeCount == 0) return 0.0;
    return freeCount / maxFreeCount;
  }

  /// 获取当前会话消息列表
  @computed
  List<FreChatMsgModel> get messages {
    return currentSession?.messages ?? [];
  }

  /// 是否可以发送消息
  @computed
  bool get canSendMessage {
    return !isSending && inputText.trim().isNotEmpty && currentSession != null;
  }

  /// 清空当前会话
  @action
  void clearCurrentSession() {
    currentSession = null;
    inputText = '';
    errorMessage = null;
    typingMessage = null;
    isLoading = false;
    isSending = false;
  }

  /// 删除消息
  @action
  void deleteMessage(String messageId) {
    currentSession?.removeMessage(messageId);
  }
}
