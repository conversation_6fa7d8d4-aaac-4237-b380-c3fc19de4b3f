import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'FrostAtelier/FreAiRoleSelectVie.dart';
import 'FrostAtelier/FreVipCenterVie.dart';
import 'FrostAtelier/FreLtianDialogVie.dart';
import 'FrostAtelier/FreLtianHistoryVie.dart';
import 'IceChips/FreVipService.dart';
import 'IceChips/FreLtianService.dart';
import 'FreshCore/FreAiRoleModel.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置状态栏样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Color(0xFFF0F0F3),
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 初始化用户服务
  await FreVipService.instance.initialize();

  // 初始化聊天服务
  await FreLtianService.instance.initialize();

  runApp(const FreApp());
}

class FreApp extends StatelessWidget {
  const FreApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Fresx',
      debugShowCheckedModeBanner: false,
      theme: _buildAppTheme(),
      initialRoute: '/',
      routes: _buildRoutes(),
      onGenerateRoute: _onGenerateRoute,
    );
  }

  // 构建应用主题（新拟物化风格）
  ThemeData _buildAppTheme() {
    return ThemeData(
      useMaterial3: true,

      // 主色调配置
      primarySwatch: Colors.green,
      primaryColor: const Color(0xFF2ECC71),

      // 背景色配置
      scaffoldBackgroundColor: const Color(0xFFF0F0F3),

      // 颜色方案
      colorScheme: const ColorScheme.light(
        primary: Color(0xFF2ECC71),      // 清新薄荷绿
        secondary: Color(0xFFF39C12),    // 亮橙色
        tertiary: Color(0xFFF7DC6F),     // 柠檬黄
        surface: Color(0xFFF0F0F3),      // 背景浅灰
        error: Color(0xFFE74C3C),        // 亮红色
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Color(0xFF333333),    // 深炭灰
        onError: Colors.white,
      ),

      // 文本主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: Color(0xFF333333),
          letterSpacing: 0.5,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: Color(0xFF333333),
          letterSpacing: 0.5,
        ),
        displaySmall: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Color(0xFF333333),
          letterSpacing: 0.5,
        ),
        headlineLarge: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: Color(0xFF333333),
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Color(0xFF333333),
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF333333),
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Color(0xFF333333),
        ),
        titleMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Color(0xFF333333),
        ),
        titleSmall: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: Color(0xFF666666),
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: Color(0xFF333333),
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: Color(0xFF666666),
          height: 1.4,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: Color(0xFF888888),
          height: 1.3,
        ),
      ),

      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFFF0F0F3),
        foregroundColor: Color(0xFF333333),
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Color(0xFF333333),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),

      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFFF0F0F3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(
            color: Color(0xFF2ECC71),
            width: 2,
          ),
        ),
        hintStyle: const TextStyle(
          color: Color(0xFF888888),
          fontSize: 14,
        ),
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2ECC71),
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),
    );
  }

  // 构建路由配置
  Map<String, WidgetBuilder> _buildRoutes() {
    return {
      '/': (context) => const FreAiRoleSelectVie(),
      '/ai-roles': (context) => const FreAiRoleSelectVie(),
      '/profile': (context) => const FreVipCenterVie(),
      '/history': (context) => const FreLtianHistoryVie(),
    };
  }

  // 处理动态路由（如聊天页面需要传递参数）
  Route<dynamic>? _onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/chat':
        final aiRole = settings.arguments as FreAiRoleModel?;
        if (aiRole != null) {
          return MaterialPageRoute(
            builder: (context) => FreLtianDialogVie(aiRole: aiRole),
            settings: settings,
          );
        }
        break;
    }
    return null;
  }
}
